import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import { JSONNode } from '@pages/admins/discovery/jsonTransform/helper/JsonTransformHelper';

export class ActiveIqApi extends BaseApi {
  static baseUrl = BaseUrl.activeIq;

  static findAllClusterNodes(sourceId: number) {
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/data-center/clusters/nodes`, {
      sourceId: sourceId,
      resourcePath: 'api/datacenter/clusters/nodes',
    });
  }

  static findAllStorageVolumes(sourceId: number) {
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/data-center/storage/volumes`, {
      sourceId: sourceId,
      resourcePath: 'api/datacenter/storage/volumes',
    });
  }
}
