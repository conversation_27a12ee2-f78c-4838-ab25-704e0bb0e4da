{"name": "itcmdb-frontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@hello-pangea/dnd": "^16.5.0", "@mantine/core": "^7.15.2", "@mantine/dates": "^7.16.1", "@mantine/dropzone": "^7.11.1", "@mantine/form": "^7.11.1", "@mantine/hooks": "^7.16.1", "@mantine/notifications": "^7.11.1", "@mantine/spotlight": "^7.11.1", "@mantine/tiptap": "^7.12.1", "@react-keycloak/web": "^3.4.0", "@react-querybuilder/mantine": "7.0.0-alpha.6", "@reduxjs/toolkit": "^1.9.7", "@tabler/icons-react": "^2.42.0", "@testing-library/jest-dom": "^5.17.0", "@tiptap/extension-highlight": "^2.6.6", "@tiptap/extension-link": "^2.6.6", "@tiptap/extension-mention": "^2.6.6", "@tiptap/extension-subscript": "^2.6.6", "@tiptap/extension-superscript": "^2.6.6", "@tiptap/extension-text-align": "^2.6.6", "@tiptap/extension-underline": "^2.6.6", "@tiptap/pm": "^2.6.6", "@tiptap/react": "^2.6.6", "@tiptap/starter-kit": "^2.6.6", "@tiptap/suggestion": "^2.6.6", "@types/react": "^18.2.38", "@types/react-dom": "^18.2.17", "axios": "^1.8.4", "dayjs": "^1.11.10", "fast-deep-equal": "^3.1.3", "fast-xml-parser": "^4.5.0", "file-saver": "^2.0.5", "immer": "^10.1.1", "js-file-download": "^0.4.12", "jsonpath-plus": "^10.2.0", "kanban-design-system": "^1.4.42", "keycloak-js": "^23.0.0", "klona": "^2.0.6", "lodash": "^4.17.21", "path-to-regexp": "^6.3.0", "rc-tree": "^5.11.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-full-screen": "^1.1.1", "react-hook-form": "^7.54.2", "react-json-view-lite": "^2.0.1", "react-querybuilder": "7.0.0-alpha.6", "react-redux": "^8.1.3", "react-resizable-panels": "^2.1.7", "react-router-dom": "^6.20.0", "redux": "^4.2.1", "redux-saga": "^1.2.3", "styled-components": "^6.1.1", "tippy.js": "^6.3.7", "typescript": "^5.6.2", "web-vitals": "^2.1.4"}, "cracoConfig": "craco.dev.config.js", "scripts": {"start": "env-cmd -f .env.local cross-env PORT=9989 BROWSER=none EXTEND_ESLINT=true rsbuild dev", "start:fast": "env-cmd -f .env.local cross-env PORT=9989 BROWSER=none DISABLE_ESLINT_PLUGIN=true DISABLE_TYPE_CHECK=true EXTEND_ESLINT=false rsbuild dev", "build": "env-cmd -f .env.uat rsbuild build", "build-tanzu-uat": "env-cmd -f .env.uat rsbuild build", "lint": "eslint --ext .js,.jsx,.ts,.tsx .", "lint:fix": "eslint --fix --ext .js,.jsx,.ts,.tsx  .", "eslint": "eslint --ext .js,.jsx,.ts,.tsx .", "eslint:fix": "eslint --fix --ext .js,.jsx,.ts,.tsx  .", "test": "craco test", "prepare1": "husky"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@rsbuild/core": "^1.2.16", "@rsbuild/plugin-eslint": "^1.1.1", "@rsbuild/plugin-react": "^1.1.1", "@rsbuild/plugin-sass": "^1.2.2", "@rsbuild/plugin-svgr": "^1.0.7", "@rsbuild/plugin-type-check": "^1.2.1", "@types/file-saver": "^2.0.7", "@types/lodash": "^4.14.202", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "@welldone-software/why-did-you-render": "^8.0.1", "cross-env": "^7.0.3", "env-cmd": "^10.1.0", "eslint": "^8.35.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-check-file": "^2.8.0", "eslint-plugin-filenames-simple": "^0.9.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-sort-destructure-keys": "^1.3.5", "eslint-plugin-unused-imports": "^3.0.0", "husky": "^9.0.11", "postcss": "^8.4.31", "postcss-preset-mantine": "^1.11.0", "postcss-simple-vars": "^7.0.1", "prettier": "^3.2.5"}, "overrides": {"serialize-javascript": "6.0.2", "express": "^4.21.0", "send": "^0.19.0", "body-parser": "^1.20.3", "webpack": "^5.95.0", "react-scripts": {"keycloak-js": "^8.4.38", "css-what": "^6.1.0", "nth-check": "^2.1.1", "postcss": "^8.4.38", "unset-value": "^2.0.1"}, "craco-esbuild": {"braces": "^3.0.3"}, "sort-by": {"object-path": "^0.11.8"}}}