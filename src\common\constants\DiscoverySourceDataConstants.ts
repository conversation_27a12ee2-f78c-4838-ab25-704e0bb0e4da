// export const DescriptionToEnumMap = new Map<string, DiscoverySourceTypeEnum>([
//   ['K8s microservice application', DiscoverySourceTypeEnum.DISCOVER_SOURCE_RANCHER_K8S_PROJECT],
//   ['Tanzu cluster node', DiscoverySourceTypeEnum.DISCOVER_SOURCE_RANCHER_TANZU_NODE],
//   ['Tanzu clusters', DiscoverySourceTypeEnum.DISCOVER_SOURCE_RANCHER_TANZU_CLUSTERS],
//   ['Netbox virtual machine', DiscoverySourceTypeEnum.DISCOVER_SOURCE_NETBOX_VIRTUALIZATION_VIRTUAL_MACHINE],
//   ['Netbox virtual disk', DiscoverySourceTypeEnum.DISCOVER_SOURCE_NETBOX_VIRTUALIZATION_VIRTUAL_DISK],
//   ['Netbox devices', DiscoverySourceTypeEnum.DISCOVER_SOURCE_NETBOX_DICM_DEVICE],
//   ['Netbox virtual cluster', DiscoverySourceTypeEnum.DISCOVER_SOURCE_NETBOX_VIRTUALIZATION_CLUSTER],
//   ['Netbox devices (role=sever)', DiscoverySourceTypeEnum.DISCOVER_SOURCE_NETBOX_DICM_DEVICE_ROLE_SERVER],
//   ['Aix hmc logical partition', DiscoverySourceTypeEnum.DISCOVERY_SOURCE_AIX_HMC_LOGICAL_PARTITION],
//   ['Vrops Clusters', DiscoverySourceTypeEnum.DISCOVERY_SOURCE_VROPS_CLUSTERS],
//   ['Vrops Resources', DiscoverySourceTypeEnum.DISCOVERY_SOURCE_VROPS_RESOURCES],
//   ['ActiveIq volumes', DiscoverySourceTypeEnum.DISCOVERY_SOURCE_ACTIVE_IQ_STORAGE_VOLUMES],
//   ['Check point', DiscoverySourceTypeEnum.DISCOVERY_SOURCE_SNMP_NETWORK_CHECKPOINT],
//   ['Paloalto', DiscoverySourceTypeEnum.DISCOVERY_SOURCE_SNMP_NETWORK_PALOALTO],
//   ['F5', DiscoverySourceTypeEnum.DISCOVERY_SOURCE_SNMP_F5],
//   ['Solarwind', DiscoverySourceTypeEnum.DISCOVERY_SOURCE_SOLARWIND_NETWORK_USER_DB],
// ]);
