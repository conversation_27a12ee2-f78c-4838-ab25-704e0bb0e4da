import {
  ApiType,
  AuthenticationType,
  DbType,
  LastDiscoveryStatusEnum,
  IntegrateMethodEnum as Method,
  SnmpType,
  SnmpTypeOid,
  SnmpVersion,
} from '@common/constants/DiscoverySourceConfigEnum';
import type { EntityModelBase } from '@models/EntityModelBase';
import { DiscoverySourceResponse } from './DiscoverySource';
import { ComboboxParsedItemGroup } from '@mantine/core';
import { DiscoverySourceTypeEnum } from '@common/constants/DiscoverySourceTypeEnum';

export type DiscoverySourceConfigModel = EntityModelBase & {
  id: number;
  sourceId: number;
  tokenKey: string;
  usernameAuthen: string;
  passwordAuthen: string;
  limit: number;
};

export type DiscoverySourceConfigResponse = DiscoverySourceConfigModel;

export type Option = {
  name: string;
  value: string;
};

export type Param = Option & {
  type: string;
};
export type SnmpOidInfo = {
  type: SnmpTypeOid;
  oid: string;
  label: string;
};
export const defaultSnmpOidInfo: SnmpOidInfo = {
  type: SnmpTypeOid.SCALAR,
  oid: '',
  label: '',
};

export type SourceConfigBasicInfo = {
  name: string;
  method: Method | undefined;
  type: DiscoverySourceTypeEnum | undefined;
  description?: string;
  snmpVersion?: SnmpVersion;
  dbType: DbType;
};

export const defaultBasicInfo: SourceConfigBasicInfo = {
  name: '',
  method: undefined,
  type: undefined,
  snmpVersion: undefined,
  dbType: DbType.MSSQL,
};

export type SourceConfigApiInfo = {
  apiUrl?: string;
  authenticationType?: AuthenticationType;
  username?: string;
  password?: string;
  tokenKey?: string;
  params?: Param[];
};

export const defaultApiInfo: SourceConfigApiInfo = {
  apiUrl: '',
  authenticationType: undefined,
  username: '',
  password: '',
  tokenKey: '',
  params: [],
};

export type SourceConfigDatabaseInfo = {
  server?: string;
  port?: string;
  databaseName?: string;
  username?: string;
  password?: string;
  // extraOptions?: Option[];
};

export const defaultDatabaseInfo: SourceConfigDatabaseInfo = {
  server: '',
  port: '',
  databaseName: '',
  username: '',
  password: '',
  // extraOptions: [],
};
export type SourceConfigSnmpInfo = {
  id: string;
  hostName: string;
  version?: SnmpType;
  username?: string;
  password?: string;
  community?: string;
  authentication?: string;
  encryption?: string;
  encryptionKey?: string;
  oidsInfo: SnmpOidInfo[];
};

export const defaultSnmpInfo: SourceConfigSnmpInfo = {
  id: '',
  hostName: '',
  version: undefined,
  username: undefined,
  password: '',
  community: '',
  authentication: undefined,
  encryption: undefined,
  encryptionKey: '',
  oidsInfo: [defaultSnmpOidInfo],
};
export type DiscoverySourceConfigDetail = {
  basicInfo: SourceConfigBasicInfo;
  apiInfo?: SourceConfigApiInfo;
  databaseInfo?: SourceConfigDatabaseInfo;
  snmpInfos?: SourceConfigSnmpInfo[];
};

export const defaultDiscoverySourceConfigDetail: DiscoverySourceConfigDetail = {
  basicInfo: defaultBasicInfo,
};

export const discoverySourceConfigDetailDefault: DiscoverySourceResponse = {
  id: 0,
  name: '',
  method: Method.API,
  type: undefined,
  lastDiscoveryStatus: LastDiscoveryStatusEnum.SUCCESS,
  lastDiscoveryTime: new Date(),
  dbType: DbType.MSSQL,
};

export const rawDataSource: Record<ApiType, DiscoverySourceTypeEnum[]> = {
  [ApiType.RANCHER]: [
    DiscoverySourceTypeEnum.DISCOVER_SOURCE_RANCHER_K8S_PROJECT,
    DiscoverySourceTypeEnum.DISCOVER_SOURCE_RANCHER_TANZU_NODE,
    DiscoverySourceTypeEnum.DISCOVER_SOURCE_RANCHER_TANZU_CLUSTERS,
  ],
  [ApiType.NETBOX]: [
    DiscoverySourceTypeEnum.DISCOVER_SOURCE_NETBOX_VIRTUALIZATION_VIRTUAL_MACHINE,
    DiscoverySourceTypeEnum.DISCOVER_SOURCE_NETBOX_VIRTUALIZATION_VIRTUAL_DISK,
    DiscoverySourceTypeEnum.DISCOVER_SOURCE_NETBOX_DICM_DEVICE,
    DiscoverySourceTypeEnum.DISCOVER_SOURCE_NETBOX_VIRTUALIZATION_CLUSTER,
    DiscoverySourceTypeEnum.DISCOVER_SOURCE_NETBOX_DICM_DEVICE_ROLE_SERVER,
  ],
  [ApiType.HMC]: [DiscoverySourceTypeEnum.DISCOVERY_SOURCE_AIX_HMC_LOGICAL_PARTITION],
  [ApiType.VROPS]: [
    DiscoverySourceTypeEnum.DISCOVERY_SOURCE_VROPS_CLUSTERS,
    DiscoverySourceTypeEnum.DISCOVERY_SOURCE_VROPS_RESOURCES,
    DiscoverySourceTypeEnum.DISCOVERY_SOURCE_VROPS_HOST,
  ],
  [ApiType.ACTIVEIQ]: [DiscoverySourceTypeEnum.DISCOVERY_SOURCE_ACTIVE_IQ_STORAGE_VOLUMES],
};

export const sourceCombobox: ComboboxParsedItemGroup[] = Object.entries(rawDataSource).map(([group, items]) => ({
  group,
  items: items.map((item) => ({
    label: item,
    value: item,
  })),
}));
