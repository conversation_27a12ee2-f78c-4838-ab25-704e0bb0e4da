import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { ColumnType, KanbanButton, KanbanSwitch, KanbanTable, KanbanText, TableAffactedSafeType, useKanbanModals } from 'kanban-design-system';
import { IconEdit, IconEye } from '@tabler/icons-react';
import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { KanbanIconButton } from 'kanban-design-system';
import { NotificationError, NotificationSuccess } from '@common/utils/NotificationUtils';
import { useNavigate } from 'react-router-dom';
import { buildCiAbsentRuleDetailUrl, buildCiTypeUrl } from '@common/utils/RouterUtils';
import equal from 'fast-deep-equal';
import { RuleStatus } from '@models/CiReconciliationRule';
import { Tooltip } from '@mantine/core';
import { BreadcrumbComponent } from '@pages/admins/breadcrumb/BreadcrumbComponent';
import { tableAffectedToMultiColumnFilterPaginationRequestModel } from '@common/utils/KanbanTableUtils';
import { MAX_NUMBER_LENGTH, MAX_TEXT_LENGTH } from '@common/constants/FieldLengthConstants';
import { CiAbsentRule, CiAbsentRuleAction, getIntervalUnitText } from '@models/CiAbsentRule';
import { CiAbsentRuleApi } from '@api/CiAbsentRuleApi';

export const CiAbsentRulePage = () => {
  const navigate = useNavigate();

  const [totalRecords, setTotalRecords] = useState(0);
  const [tableAffected, setTableAffected] = useState<TableAffactedSafeType | undefined>(undefined);

  const [listData, setListData] = useState<CiAbsentRule[]>([]);
  const [isLoadingTable, setIsLoadingTable] = useState(false);

  const columns: ColumnType<CiAbsentRule>[] = useMemo(
    () => [
      {
        title: 'Id',
        name: 'id',
        hidden: true,
        advancedFilter: {
          variant: 'number',
          filterModes: ['equals', 'notEquals', 'greaterThan', 'greaterThanOrEqualTo', 'lessThan', 'lessThanOrEqualTo'],
          customProps: { maxLength: MAX_NUMBER_LENGTH },
        },
      },
      {
        title: 'Name',
        name: 'name',
        width: '10%',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
      },
      {
        title: 'Apply to',
        name: 'ciTypeName',
        width: '15%',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
        customRender: (_, rowData) => {
          const ciTypeName = rowData?.ciTypeName || 'Not Available';
          return (
            <Tooltip label={ciTypeName} multiline w={350} style={{ wordBreak: 'break-word' }}>
              <KanbanButton
                size='compact-xs'
                radius={'lg'}
                maw={'200px'}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  navigate(buildCiTypeUrl(rowData.id));
                }}>
                {ciTypeName}
              </KanbanButton>
            </Tooltip>
          );
        },
      },
      {
        title: 'Status',
        name: 'status',
        width: '10%',
        advancedFilter: {
          enable: false,
        },
        customRender: (data) => {
          const isCheck = RuleStatus.ENABLE === data;
          return (
            <KanbanButton size='compact-xs' variant={'subtle'} c={'white'} fw={'700'} bg={isCheck ? 'green' : 'red'}>
              {data}
            </KanbanButton>
          );
        },
      },
      {
        title: 'Absence Threshold',
        name: 'absenceThreshold',
        width: '10%',
        advancedFilter: {
          variant: 'number',
          filterModes: ['equals', 'notEquals', 'greaterThan', 'greaterThanOrEqualTo', 'lessThan', 'lessThanOrEqualTo'],
          customProps: { maxLength: MAX_NUMBER_LENGTH },
        },
        customRender: (data, rowData) => {
          return <KanbanText lineClamp={2}>{`${data} ${getIntervalUnitText(rowData.absenceThresholdUnit)}`}</KanbanText>;
        },
      },
      {
        title: 'Disposed Threshold',
        name: 'disposedThreshold',
        width: '10%',
        advancedFilter: {
          variant: 'number',
          filterModes: ['equals', 'notEquals', 'greaterThan', 'greaterThanOrEqualTo', 'lessThan', 'lessThanOrEqualTo'],
          customProps: { maxLength: MAX_NUMBER_LENGTH },
        },
        customRender: (data, rowData) => {
          return <KanbanText lineClamp={2}>{`${data} ${getIntervalUnitText(rowData.disposedThresholdUnit)}`}</KanbanText>;
        },
      },
      {
        title: 'Description',
        name: 'description',
        width: '40%',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
        customRender: (data: string) => {
          return (
            <>
              <Tooltip label={data} color='gray' multiline w={'20%'} withArrow>
                <KanbanText lineClamp={2}>{data}</KanbanText>
              </Tooltip>
            </>
          );
        },
      },
    ],
    [navigate],
  );

  const findAllWithPaging = useCallback(() => {
    if (!tableAffected) {
      return;
    }
    const dataSend = tableAffectedToMultiColumnFilterPaginationRequestModel<CiAbsentRule>(
      tableAffected.sortedBy ? tableAffected : { ...tableAffected, sortedBy: 'id', isReverse: true },
    );

    setIsLoadingTable(true);
    CiAbsentRuleApi.findAllWithPaging(dataSend)
      .then((res) => {
        if (res.data) {
          setListData(res.data?.content || []);
          setTotalRecords(res.data.totalElements);
        }
      })
      .catch(() => {})
      .finally(() => {
        setIsLoadingTable(false);
      });
  }, [tableAffected]);

  useEffect(() => {
    findAllWithPaging();
  }, [findAllWithPaging]);

  const deleteRows = (ids: number[]) => {
    CiAbsentRuleApi.deleteByIdIn(ids)
      .then(() => {
        NotificationSuccess({
          message: 'Deleted successfully',
        });
        const newData = listData.filter((item) => !ids.includes(item.id));
        setListData(newData);
      })
      .catch(() => {});
  };

  const handleUpdateStatus = (ruleInfo: CiAbsentRule, ruleId: number) => {
    CiAbsentRuleApi.updateRule(ruleInfo, ruleId)
      .then((res) => {
        if (res.data) {
          NotificationSuccess({
            message: 'Updated successfully.',
          });
        } else {
          NotificationError({
            message: 'Error when update rule.',
          });
        }
      })
      .catch(() => {});
  };

  const modelProvider = useKanbanModals();

  return (
    <>
      {/* 4746 ci rules*/}
      <BreadcrumbComponent />
      <HeaderTitleComponent title='CI Absent' />
      <div style={{ flex: 2 }}>
        <KanbanTable
          columns={columns}
          key={1}
          data={listData}
          isLoading={isLoadingTable}
          showNumericalOrderColumn={true}
          searchable={{
            enable: true,
            debounceTime: 800,
          }}
          advancedFilterable={{
            enable: true,
            debounceTime: 1000,
            resetOnClose: true,
            compactMode: true,
          }}
          onRowClicked={(data) => {
            navigate(buildCiAbsentRuleDetailUrl(data.id, CiAbsentRuleAction.VIEW));
          }}
          actions={{
            customAction: (data) => {
              return (
                <>
                  <KanbanSwitch
                    size='xs'
                    mr='xs'
                    color={'green'}
                    checked={RuleStatus.ENABLE === data.status}
                    onChange={(e) => {
                      const value = e.currentTarget.checked;

                      const actionStatus = value ? RuleStatus.ENABLE : RuleStatus.DISABLE;
                      const idModelStatus = modelProvider.openConfirmModal({
                        title: `Confirm ${actionStatus.toLowerCase()} absent rule`,
                        children: `Are you sure to ${actionStatus.toLowerCase()} these item(s)?`,
                        onConfirm: () => {
                          const updatedList = listData.map((item) => (item.id === data.id ? { ...item, status: actionStatus } : item));
                          setListData(updatedList);

                          const updatedObj: CiAbsentRule = { ...data, status: actionStatus };
                          handleUpdateStatus(updatedObj, data.id);

                          modelProvider.closeModal(idModelStatus);
                        },
                      });
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                    }}
                  />
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      navigate(buildCiAbsentRuleDetailUrl(data.id, CiAbsentRuleAction.VIEW));
                    }}>
                    <IconEye />
                  </KanbanIconButton>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      navigate(buildCiAbsentRuleDetailUrl(data.id, CiAbsentRuleAction.UPDATE));
                    }}>
                    <IconEdit />
                  </KanbanIconButton>
                </>
              );
            },
          }}
          selectableRows={{
            enable: false,
            onDeleted(rows) {
              deleteRows(rows.map((x) => x.id));
            },
          }}
          pagination={{
            enable: true,
          }}
          serverside={{
            totalRows: totalRecords,
            onTableAffected(dataSet) {
              if (!equal(tableAffected, dataSet)) {
                setTableAffected(dataSet);
              }
            },
          }}
        />
      </div>
    </>
  );
};
CiAbsentRulePage.whyDidYouRender = true;
CiAbsentRulePage.displayName = 'CiAbsentRulePage';
export default CiAbsentRulePage;
