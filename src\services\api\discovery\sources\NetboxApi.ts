import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import { JSONNode } from '@pages/admins/discovery/jsonTransform/helper/JsonTransformHelper';

export class NetboxApi extends BaseApi {
  static baseUrl = BaseUrl.netboxs;

  static findVirtualMachineById(id: string) {
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/virtual-machines/${id}`);
  }

  static findVirtualMachines(sourceId: number) {
    return BaseApi.getData<JSONNode>(`${this.baseUrl}`, {
      sourceId,
      resourcePath: 'api/virtualization/virtual-machines/',
    });
  }

  static findVirtualDisks(sourceId: number) {
    return BaseApi.getData<JSONNode>(`${this.baseUrl}`, {
      sourceId,
      resourcePath: 'api/virtualization/virtual-disks/',
    });
  }

  static findVirtualClusters(sourceId: number) {
    return BaseApi.getData<JSONNode>(`${this.baseUrl}`, {
      sourceId,
      resourcePath: 'api/virtualization/clusters/',
    });
  }

  static findDevices(sourceId: number) {
    return BaseApi.getData<JSONNode>(`${this.baseUrl}`, {
      sourceId,
      resourcePath: 'api/dcim/devices/',
    });
  }
}
