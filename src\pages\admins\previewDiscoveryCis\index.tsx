import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  ColumnType,
  KanbanButton,
  KanbanConfirmModal,
  KanbanModal,
  KanbanTableSelectHandleMethods,
  KanbanText,
  KanbanTooltip,
  renderDateTime,
  type TableAffactedSafeType,
} from 'kanban-design-system';
import { KanbanTable, type KanbanTableProps } from 'kanban-design-system';
import { IconArrowBack, IconCirclePlus, IconEye } from '@tabler/icons-react';
import { KanbanIconButton } from 'kanban-design-system';
import { Badge, Divider, Flex, Group, Stack, Tooltip } from '@mantine/core';
import { DiscoveryPreviewDataCiApi } from '@api/discovery/DiscoveryPreviewDataCiApi';
import { tableAffectedToMultiColumnFilterPaginationRequestModel } from '@common/utils/KanbanTableUtils';
import { DiscoveryPreviewDataCiGroupModel, DiscoveryPreviewDataCiModel } from '@models/DiscoveryPreviewDataCi';
import equal from 'fast-deep-equal';
import {
  buildCiManageDetailUrl,
  buildCiTypeUrl,
  buildCiUrl,
  buildDiscoveryPreviewDataCiGroup,
  buildDiscoveryPreviewDataCiGroupDetailPath,
} from '@common/utils/RouterUtils';
import {
  DiscoveryDataCiTransformStatus,
  DiscoveryPreviewDataCiAction,
  DiscoveryPreviewDataCiStatusIdentifier,
} from '@common/constants/DiscoveryPreviewDataCiConstants';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { DataChangeObj, formatValueCiAttribute } from '@pages/cis/ciManagement/detail/CiNoticeChange';
import { Attributes } from '@models/CiDetailCompare';
import { CiManagementExecute } from '@service/CiManagementExecute';
import { ConfigItemTypeAttrResponse } from '@api/ConfigItemTypeAttrApi';
import { useDisclosure } from '@mantine/hooks';
import DiscoveryCiDataDetailPage from './DiscoveryCiDataDetailPage';
import styled from './PreviewDicovery.module.scss';
import { NotificationSuccess } from '@common/utils/NotificationUtils';
import { BreadcrumbComponent, UrlBaseCrumbData } from '../breadcrumb/BreadcrumbComponent';
import { CiManagementApi } from '@api/CiManagementApi';
import { dateToString, DD_MM_YYYY_HH_MM_SS_FORMAT } from '@common/utils/DateUtils';
import { getPreviewJobName, mapDiscoveryMethodColor } from './PreviewDiscoveryCiGroupPage';
import { formatSmartNumber } from '@common/utils/CommonUtils';
import { MAX_NUMBER_LENGTH, MAX_TEXT_LENGTH } from '@common/constants/FieldLengthConstants';
import { CiDetailScreenType } from '@common/constants/CiDetail';
const getChange = (attributes: Attributes, listCiTypeAttribute: ConfigItemTypeAttrResponse[]) => {
  const result: DataChangeObj[] = [];
  for (const key in attributes) {
    const attribute = attributes[key];
    const objAttribute = listCiTypeAttribute.find((x) => String(x.id) === key);
    const keyName = objAttribute ? objAttribute.name : key;

    const oldValue = String(attribute.oldValue);
    const valueStrOld = formatValueCiAttribute(oldValue, objAttribute);
    const newValue = String(attribute.newValue);
    const valueStrNew = formatValueCiAttribute(newValue, objAttribute);
    result.push({ key: keyName, oldValue: valueStrOld, newValue: valueStrNew });
  }
  return result;
};

export const renderDciAction = (rowData: DiscoveryPreviewDataCiModel) => {
  return (
    <KanbanButton
      size='compact-xs'
      variant={'subtle'}
      c={'white'}
      fw={'700'}
      bg={
        DiscoveryPreviewDataCiAction.CREATE === rowData.action
          ? 'primary'
          : DiscoveryPreviewDataCiAction.UPDATE === rowData.action
            ? 'yellow'
            : 'orange'
      }>
      {rowData.action}
    </KanbanButton>
  );
};

type StatusColorMap = {
  [key in DiscoveryDataCiTransformStatus]: string;
};

const mapStatusColor: StatusColorMap = {
  [DiscoveryDataCiTransformStatus.PENDING]: 'blue',
  [DiscoveryDataCiTransformStatus.FAILED]: 'var(--mantine-color-dark-3)',
  [DiscoveryDataCiTransformStatus.DRAFT]: 'orange',
  [DiscoveryDataCiTransformStatus.WAITING]: 'yellow',
  [DiscoveryDataCiTransformStatus.APPROVED]: 'green',
  [DiscoveryDataCiTransformStatus.REJECTED]: 'red',
  [DiscoveryDataCiTransformStatus.DELETED]: 'var(--mantine-color-gray-5)',
};

const listActionAllowCreateCi: DiscoveryPreviewDataCiAction[] = [DiscoveryPreviewDataCiAction.CREATE, DiscoveryPreviewDataCiAction.UPDATE];

export const DiscoveryPreviewDataCiListPage: React.FC = () => {
  //avoid when whenever flag=>  dont call fetch unnessasry
  // const prevIdsRef = useRef<number[]>(listImpactedCi.map((item) => item.ciImpactedId ?? 0));
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const [totalRecords, setTotalRecords] = useState(0);
  const [tableAffected, setTableAffected] = useState<TableAffactedSafeType>();

  const [listDci, setListDci] = useState<DiscoveryPreviewDataCiModel[]>([]);
  const [summaryDataInfo, setSummaryDataInfo] = useState<DiscoveryPreviewDataCiGroupModel>();

  const [openedPreviewDciPopup, { close: closePreviewDciPopup, open: openPreviewDciPopup }] = useDisclosure(false);
  const [currentPreviewDCI, setCurrentPreviewDCI] = useState<DiscoveryPreviewDataCiModel>();
  const [modalConfirmMergeData, { close: closeModalConfirmMergeData, open: openModalConfirmMergeData }] = useDisclosure(false);
  const [currentDciId, setCurrentDciId] = useState<number>(0);
  const tableRef = useRef<KanbanTableSelectHandleMethods>(null);

  const jobHistoryIdParam = searchParams.get('jobHistoryId')?.trim() || '';
  const summaryIdParam = searchParams.get('summaryId')?.trim() || '';
  const jobHistoryId = Number(jobHistoryIdParam);
  const summaryId = Number(summaryIdParam);

  const renderDiscoveryCi = useCallback((rowData: DiscoveryPreviewDataCiModel) => {
    //case CREATE this show identifier rule name , case update this show ci live name , all logic handled in BE
    const updateMain = DiscoveryPreviewDataCiAction.UPDATE === rowData.action && rowData.ciId;
    const createNew = DiscoveryPreviewDataCiAction.CREATE === rowData.action && !rowData.ciId;
    const createDuplicate = DiscoveryPreviewDataCiAction.DUPLICATE === rowData.action && rowData.ciId;

    const ciOrDciNameRender = (name?: string) => {
      return (
        <KanbanTooltip label={name} w={300} multiline>
          <KanbanText mr={'xs'} fz={'xs'} className={styled.clipText}>
            {name}
          </KanbanText>
        </KanbanTooltip>
      );
    };
    if (updateMain) {
      return rowData.ciName && <Flex direction={'row'}>{ciOrDciNameRender(rowData.ciName)}</Flex>;
    }
    if (createNew) {
      return (
        rowData.sourceDiscoveryCiName && (
          <Flex direction={'row'}>
            {ciOrDciNameRender(rowData.sourceDiscoveryCiName)}
            <KanbanButton size='compact-xs' bg={'green'} c={'white'}>
              main
            </KanbanButton>
          </Flex>
        )
      );
    }
    if (createDuplicate) {
      return (
        rowData.sourceDiscoveryCiName && (
          <Flex direction={'row'}>
            {ciOrDciNameRender(rowData.sourceDiscoveryCiName)}

            <KanbanButton size='compact-xs' bg={'gray'} c={'white'}>
              duplicate
            </KanbanButton>
          </Flex>
        )
      );
    }
  }, []);

  const fetchListDcis = useCallback(
    (controller?: AbortController) => {
      if (!tableAffected) {
        return;
      }

      const dataSend = tableAffectedToMultiColumnFilterPaginationRequestModel<DiscoveryPreviewDataCiModel>(
        tableAffected.sortedBy ? tableAffected : { ...tableAffected, sortedBy: 'statusIdentifier', isReverse: false },
      );

      const queryParams: Record<string, any> = {};
      queryParams['summaryId'] = summaryId;
      queryParams['jobHistoryId'] = jobHistoryId;
      DiscoveryPreviewDataCiApi.getAllDiscoveryPreviewDataCi(dataSend, queryParams, controller)
        .then((res) => {
          if (res.data) {
            const data = res.data;
            if (data) {
              const updateList = data.content.map((it) => {
                return {
                  ...it,
                  listCiAttributeChange: getChange(
                    CiManagementExecute.compareAttributes(it.listCiAttributeLive || [], it.listCiAttributeDiscovered || []),
                    it.listCiTypeAttribute || [],
                  ).map((it) => `${it.key} - ${it.oldValue} - ${it.newValue}`),
                };
              });
              setListDci(updateList);
              setTotalRecords(data.totalElements);
            }
          }
        })
        .catch(() => {});
    },
    [jobHistoryId, summaryId, tableAffected],
  );

  const processCreateNewCi = useCallback(
    (dciId: number) => {
      if (dciId > 0) {
        DiscoveryPreviewDataCiApi.transformDataDiscoveryToCi(dciId)
          .then((res) => {
            if (res.data) {
              NotificationSuccess({
                message: 'Transform data discovery to CI successfully.',
              });
            }
          })
          .catch(() => {})
          .finally(() => {
            fetchListDcis();
          });
      }
    },
    [fetchListDcis],
  );

  const createNewCi = useCallback(
    (rowData: DiscoveryPreviewDataCiModel) => {
      if (DiscoveryPreviewDataCiAction.UPDATE === rowData.action) {
        processCreateNewCi(rowData.id);
      } else {
        CiManagementApi.findByNameCiTemp(0, rowData.ciTypeId || 0, rowData.sourceDiscoveryCiName || '')
          .then((res) => {
            if (res.data) {
              openModalConfirmMergeData();
              return;
            } else {
              processCreateNewCi(rowData.id);
              return;
            }
          })
          .catch(() => {});
      }
    },
    [openModalConfirmMergeData, processCreateNewCi],
  );

  const columns: ColumnType<DiscoveryPreviewDataCiModel>[] = useMemo(() => {
    const cols: ColumnType<DiscoveryPreviewDataCiModel>[] = [
      {
        title: 'ID',
        name: 'id',
        width: '5%',
        hidden: true,
        advancedFilter: {
          variant: 'number',
          filterModes: ['equals', 'notEquals', 'greaterThan', 'greaterThanOrEqualTo', 'lessThan', 'lessThanOrEqualTo'],
          customProps: { maxLength: MAX_NUMBER_LENGTH },
        },
      },
      {
        title: 'Schedule History Id',
        name: 'jobHistoryId',
        width: '5%',
        hidden: true,
        advancedFilter: {
          enable: false,
        },
        customRender: (data: any) => {
          return <KanbanText>{data || `__`}</KanbanText>;
        },
      },
      {
        title: 'Time',
        name: 'createdDate',
        width: '10%',
        customRender: renderDateTime,
        advancedFilter: {
          variant: 'date',
          customProps: {
            popoverProps: {
              withinPortal: false,
            },
          },
        },
      },
      {
        title: 'Target(CI Type)',
        name: 'ciTypeName',
        width: '10%',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
        customRender: (data: string, rowData: DiscoveryPreviewDataCiModel) => {
          return (
            <KanbanButton
              size='compact-xs'
              variant={'subtle'}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                window.open(buildCiTypeUrl(rowData.ciTypeId || 0), '_blank');
              }}>
              {rowData.ciTypeName}
            </KanbanButton>
          );
        },
      },
      {
        title: 'Transform Map',
        name: 'transformMapName',
        width: '10%',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
      },
      {
        title: 'CI Live',
        name: 'ciName',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
        customRender: (_: string, rowData: DiscoveryPreviewDataCiModel) => {
          return (
            rowData.ciId && (
              <Flex direction={'row'}>
                <KanbanButton
                  size='compact-xs'
                  variant={'subtle'}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    window.open(buildCiUrl(rowData.ciTypeId || 0, rowData.ciId || 0, CiDetailScreenType.DISCOVERY), '_blank');
                  }}>
                  {rowData.ciName}
                </KanbanButton>
                <KanbanButton size='compact-xs' bg={'green'} c={'white'}>
                  main
                </KanbanButton>
              </Flex>
            )
          );
        },
      },
      {
        title: 'CI Discovery',
        name: 'sourceDiscoveryCiName',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
        customRender: (data: string, rowData: DiscoveryPreviewDataCiModel) => {
          return renderDiscoveryCi(rowData);
        },
      },

      {
        title: 'Activity',
        name: 'action',
        width: '10%',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
        customRender: (_: string, rowData: DiscoveryPreviewDataCiModel) => {
          return renderDciAction(rowData);
        },
      },
      {
        title: 'Status Identifier',
        name: 'statusIdentifier',
        width: '10%',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
        customRender: (_: string, rowData: DiscoveryPreviewDataCiModel) => {
          return (
            <KanbanButton
              size='compact-xs'
              variant={'subtle'}
              c={'white'}
              fw={'700'}
              bg={DiscoveryPreviewDataCiStatusIdentifier.SUCCESS === rowData.statusIdentifier ? 'green' : 'red'}>
              {rowData.statusIdentifier}
            </KanbanButton>
          );
        },
      },
      {
        title: 'Status',
        name: 'status',
        width: '10%',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
        customRender: (data: DiscoveryDataCiTransformStatus, rowData: DiscoveryPreviewDataCiModel) => {
          return (
            <>
              <Flex gap={5} justify={'space-between'}>
                <KanbanButton size='compact-xs' variant={'subtle'} c={'white'} fw={'700'} bg={mapStatusColor[data]}>
                  {data}
                </KanbanButton>
                {rowData.ciTempId && (
                  <KanbanButton
                    size='compact-xs'
                    variant={'outline'}
                    c={'--mantine-color-cyan-3'}
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      window.open(buildCiManageDetailUrl(rowData.ciTempId || 0), '_blank');
                    }}>
                    {rowData.ciTempId}
                  </KanbanButton>
                )}
              </Flex>
            </>
          );
        },
      },
      {
        title: 'Description',
        hidden: true,
        name: 'description',
        width: '20%',
        advancedFilter: {
          variant: 'text',
          filterModes: ['contains', 'startsWith', 'endsWith'],
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
      },
    ];

    return cols;
  }, [renderDiscoveryCi]);

  const handleViewDetail = useCallback(
    (rowData: DiscoveryPreviewDataCiModel) => {
      if (DiscoveryPreviewDataCiStatusIdentifier.SUCCESS === rowData.statusIdentifier) {
        openPreviewDciPopup();
        setCurrentPreviewDCI(rowData);
      }
    },
    [openPreviewDciPopup],
  );

  const tableProps: KanbanTableProps<DiscoveryPreviewDataCiModel> = useMemo(() => {
    const cols = columns;

    return {
      showNumericalOrderColumn: true,
      searchable: {
        enable: true,
        debounceTime: 1000,
      },
      sortable: {
        enable: true,
      },
      advancedFilterable: {
        enable: true,
        debounceTime: 1000,
        resetOnClose: true,
        compactMode: true,
      },
      onRowClicked: (rowData: DiscoveryPreviewDataCiModel) => {
        handleViewDetail(rowData);
      },
      columns: cols,
      data: listDci,
      key: cols,
      serverside: {
        totalRows: totalRecords,
        onTableAffected(dataSet) {
          if (!equal(tableAffected, dataSet)) {
            setTableAffected(dataSet);
          }
        },
      },
      pagination: {
        enable: true,
      },
      actions: {
        customAction: (rowData: DiscoveryPreviewDataCiModel) => {
          return (
            <>
              {DiscoveryPreviewDataCiStatusIdentifier.SUCCESS === rowData.statusIdentifier && (
                <>
                  <Tooltip label='View attribute change'>
                    <KanbanIconButton
                      variant='transparent'
                      size={'sm'}
                      onClick={() => {
                        handleViewDetail(rowData);
                      }}>
                      <IconEye />
                    </KanbanIconButton>
                  </Tooltip>
                  {DiscoveryDataCiTransformStatus.PENDING === rowData.status &&
                    rowData.action &&
                    listActionAllowCreateCi.includes(rowData.action) && (
                      <Tooltip label='Transform to CI'>
                        <KanbanIconButton
                          key={rowData.id}
                          size='sm'
                          variant='transparent'
                          onClick={() => {
                            createNewCi(rowData);
                            setCurrentDciId(rowData.id);
                          }}>
                          <IconCirclePlus />
                        </KanbanIconButton>
                      </Tooltip>
                    )}
                </>
              )}
            </>
          );
        },
      },
    };
  }, [columns, createNewCi, handleViewDetail, listDci, tableAffected, totalRecords]);

  useEffect(() => {
    const controller = new AbortController();
    fetchListDcis(controller);
    return () => controller.abort();
  }, [fetchListDcis]);

  const fetchDataSummary = useCallback(() => {
    const queryParams: Record<string, any> = {};
    queryParams['summaryId'] = summaryId;
    queryParams['jobHistoryId'] = jobHistoryId;
    DiscoveryPreviewDataCiApi.getDiscoveryPreviewDataCiGroup(queryParams)
      .then((res) => {
        if (res && res.data) {
          setSummaryDataInfo(res.data);
        }
      })
      .catch(() => {});
  }, [jobHistoryId, summaryId]);

  useEffect(() => {
    fetchDataSummary();
  }, [fetchDataSummary]);

  const locationCustomPaths = useMemo((): UrlBaseCrumbData => {
    const originPath = buildDiscoveryPreviewDataCiGroupDetailPath(jobHistoryId, summaryId);

    return {
      [`/detail`]: {
        title: 'Discovery data Ci detail',
        href: originPath,
      },
    };
  }, [jobHistoryId, summaryId]);

  const onClickReturn = useCallback(() => {
    navigate(buildDiscoveryPreviewDataCiGroup());
  }, [navigate]);

  return (
    <>
      {/* Modal confirm delete attribute */}

      <BreadcrumbComponent locationCustomPaths={locationCustomPaths} />
      <KanbanConfirmModal
        opened={modalConfirmMergeData}
        onClose={closeModalConfirmMergeData}
        title='Confirm merge data'
        onConfirm={() => {
          processCreateNewCi(currentDciId);
          closeModalConfirmMergeData();
        }}
        modalProps={{
          size: 'lg',
        }}>
        A CI Draft record with the same name as the current discovery data already exists. If you proceed, the data will be merged into the CI Draft.
      </KanbanConfirmModal>

      <KanbanModal onClose={closePreviewDciPopup} opened={openedPreviewDciPopup} size={'70%'}>
        <DiscoveryCiDataDetailPage idProp={currentPreviewDCI?.id} onClosedPopupDetail={closePreviewDciPopup} />
      </KanbanModal>

      <>
        <Group justify='flex-end' m={'md'}>
          <KanbanButton
            variant='default'
            leftSection={<IconArrowBack />}
            onClick={() => {
              onClickReturn();
            }}>
            Back
          </KanbanButton>
        </Group>
        {summaryDataInfo && (
          <Stack align='flex-start' justify='flex-start' gap={'xs'} bg={'var(--mantine-color-yellow-light)'} p={'md'}>
            <Group>
              <KanbanText>Discovery method:</KanbanText>
              <Badge color={mapDiscoveryMethodColor[summaryDataInfo?.discoveryMethod]} radius='sm' size='xs' mb={0}>
                {summaryDataInfo?.discoveryMethod}
              </Badge>
            </Group>
            <Group>
              <KanbanText>Start time:</KanbanText>
              <KanbanText>{summaryDataInfo?.startDate ? dateToString(summaryDataInfo?.startDate, DD_MM_YYYY_HH_MM_SS_FORMAT) : ``}</KanbanText>
            </Group>
            <Group>
              <KanbanText>End time:</KanbanText>
              <KanbanText>{summaryDataInfo?.endDate ? dateToString(summaryDataInfo?.endDate, DD_MM_YYYY_HH_MM_SS_FORMAT) : ``}</KanbanText>
            </Group>
            <Group>
              <KanbanText>Run time:</KanbanText>
              <KanbanText>{summaryDataInfo?.runTime ? `${formatSmartNumber(summaryDataInfo?.runTime)} (s)` : ''}</KanbanText>
            </Group>
            <Group>
              <KanbanText>Schedule:</KanbanText>
              {(() => {
                const jobName = getPreviewJobName(summaryDataInfo.jobName, summaryDataInfo);
                const isDeleted = jobName === '(Deleted)';
                return <KanbanText c={isDeleted ? 'red' : undefined}>{jobName}</KanbanText>;
              })()}
            </Group>
            <Group>
              <KanbanText>Data Source:</KanbanText>
              <KanbanText>{summaryDataInfo?.dataSourceName || ''}</KanbanText>
            </Group>
            <Group>
              <KanbanText>Run by:</KanbanText>
              <KanbanText>{summaryDataInfo?.runBy}</KanbanText>
            </Group>
            <Group>
              <KanbanText>{summaryDataInfo?.summaryDetail || ''}</KanbanText>
            </Group>
          </Stack>
        )}
      </>

      <Divider my='sm' />
      <KanbanTable ref={tableRef} {...tableProps} />
    </>
  );
};
