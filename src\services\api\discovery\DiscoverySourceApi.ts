import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import type { PaginationRequestModel, PaginationResponseModel } from '@models/EntityModelBase';
import { DiscoverySourceResponse } from '@models/discovery/DiscoverySource';
import { DiscoverySourceConfigDetail } from '@models/discovery/DiscoverySourceConfig';

export type DiscoverySourcePagingResponse = PaginationResponseModel<DiscoverySourceResponse>;
export class DiscoverySourceApi extends BaseApi {
  static baseUrl = BaseUrl.discoverySources;

  static getAllWithPaging(pagination: PaginationRequestModel<DiscoverySourcePagingResponse>) {
    return BaseApi.postData<DiscoverySourcePagingResponse>(`${this.baseUrl}/paging`, pagination);
  }

  static getAll() {
    return BaseApi.getData<DiscoverySourceResponse[]>(`${this.baseUrl}/all`);
  }

  static getById(id: number) {
    return BaseApi.getData<DiscoverySourceConfigDetail>(`${this.baseUrl}/${id}`);
  }

  static saveOrUpdate(data: DiscoverySourceConfigDetail) {
    return BaseApi.postData<DiscoverySourceResponse>(`${this.baseUrl}`, data);
  }

  static deleteByIds(ids: number[]) {
    return BaseApi.deleteData<number[]>(`${this.baseUrl}/batch`, {
      ids,
    });
  }
}
