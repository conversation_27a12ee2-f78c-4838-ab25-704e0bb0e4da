export enum DiscoverySourceTypeEnum {
  // API
  DISCOVER_SOURCE_RANCHER_K8S_PROJECT = 'DISCOVER_SOURCE_RANCHER_K8S_PROJECT',
  DISCOVER_SOURCE_RANCHER_TANZU_NODE = 'DISCOVER_SOURCE_RANCHER_TANZU_NODE',
  DISCOVER_SOURCE_RANCHER_TANZU_CLUSTERS = 'DISCOVER_SOURCE_RANCHER_TANZU_CLUSTERS',
  DISCOVER_SOURCE_NETBOX_VIRTUALIZATION_VIRTUAL_MACHINE = 'DISCOVER_SOURCE_NETBOX_VIRTUALIZATION_VIRTUAL_MACHINE',
  DISCOVER_SOURCE_NETBOX_VIRTUALIZATION_VIRTUAL_DISK = 'DISCOVER_SOURCE_NETBOX_VIRTUALIZATION_VIRTUAL_DISK',
  DISCOVER_SOURCE_NETBOX_DICM_DEVICE = 'DISCOVER_SOURCE_NETBOX_DICM_DEVICE',
  DISCOVER_SOURCE_NETBOX_VIRTUALIZATION_CLUSTER = 'DISCOVER_SOURCE_NETBOX_VIRTUALIZATION_CLUSTER',
  DISCOVER_SOURCE_NETBOX_DICM_DEVICE_ROLE_SERVER = 'DISCOVER_SOURCE_NETBOX_DICM_DEVICE_ROLE_SERVER',
  DISCOVERY_SOURCE_AIX_HMC_LOGICAL_PARTITION = 'DISCOVERY_SOURCE_AIX_HMC_LOGICAL_PARTITION',
  DISCOVERY_SOURCE_VROPS_CLUSTERS = 'DISCOVERY_SOURCE_VROPS_CLUSTERS',
  DISCOVERY_SOURCE_VROPS_RESOURCES = 'DISCOVERY_SOURCE_VROPS_RESOURCES',
  DISCOVERY_SOURCE_VROPS_HOST = 'DISCOVERY_SOURCE_VROPS_HOST',
  DISCOVERY_SOURCE_ACTIVE_IQ_STORAGE_VOLUMES = 'DISCOVERY_SOURCE_ACTIVE_IQ_STORAGE_VOLUMES',

  // SNMP
  DISCOVERY_SOURCE_SNMP_NETWORK_CHECKPOINT = 'DISCOVERY_SOURCE_SNMP_NETWORK_CHECKPOINT',
  DISCOVERY_SOURCE_SNMP_NETWORK_PALOALTO = 'DISCOVERY_SOURCE_SNMP_NETWORK_PALOALTO',
  DISCOVERY_SOURCE_SNMP_F5 = 'DISCOVERY_SOURCE_SNMP_F5',

  // DB
  DISCOVERY_SOURCE_SOLARWIND_NETWORK_USER_DB = 'DISCOVERY_SOURCE_SOLARWIND_NETWORK_USER_DB',
}
