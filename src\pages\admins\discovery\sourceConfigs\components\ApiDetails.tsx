import React, { forwardRef, useEffect, useImperativeHandle } from 'react';
import { Card, Group, Stack, ActionIcon, Flex, PasswordInput } from '@mantine/core';
import { IconPlus, IconMinus } from '@tabler/icons-react';
import { useForm, Controller, useFieldArray, useWatch } from 'react-hook-form';
import { AuthenticationType, ParamType } from '@common/constants/DiscoverySourceConfigEnum';
import { defaultApiInfo, SourceConfigApiInfo } from '@models/discovery/DiscoverySourceConfig';
import { enumToSelectData } from './BasicInfo';
import { KanbanInput, KanbanSelect, KanbanTitle } from 'kanban-design-system';
import styles from '../SourceConfig.module.scss';
import { SourceDataAction } from '@common/constants/SourceDataActionEnum';

export type ApiDetailsRef = {
  getData: () => Promise<SourceConfigApiInfo>;
};

export type ApiDetailsProps = {
  defaultData?: Partial<SourceConfigApiInfo>;
  action: SourceDataAction;
};

export const paramTypeOptions = enumToSelectData(ParamType);

const authOptions = enumToSelectData(AuthenticationType);

const ApiDetails = forwardRef<ApiDetailsRef, ApiDetailsProps>(({ action, defaultData }, ref) => {
  const { control, handleSubmit, reset } = useForm<SourceConfigApiInfo>({
    defaultValues: defaultApiInfo,
  });
  const isViewAction = SourceDataAction.VIEW === action;
  const { append, fields, remove, replace } = useFieldArray({
    control,
    name: 'params',
  });

  const authType = useWatch({ control, name: 'authenticationType' });

  useEffect(() => {
    if (defaultData) {
      reset(defaultData);
      replace(defaultData.params ?? []);
    }
  }, [defaultData, reset, replace]);

  useImperativeHandle(ref, () => ({
    getData: async () => {
      let result: SourceConfigApiInfo = {};
      await handleSubmit((data) => (result = data))();
      return result;
    },
  }));

  return (
    <Card withBorder p='md' radius='md'>
      <KanbanTitle order={4}>API Details</KanbanTitle>
      <Stack gap='md'>
        <Controller
          name='apiUrl'
          control={control}
          rules={{
            required: 'API URL is required',
            validate: (value) => {
              if (!value?.trim()) {
                return 'API URL is required';
              }
              return true;
            },
          }}
          render={({ field, fieldState }) => (
            <KanbanInput
              label='API URL'
              {...field}
              error={fieldState.error?.message}
              required
              disabled={isViewAction}
              maxLength={2000}
              onBlur={(e) => {
                const value = e.target.value;
                field.onChange(value.trim());
              }}
            />
          )}
        />

        <Controller
          name='authenticationType'
          control={control}
          rules={{ required: 'Authentication type is required' }}
          render={({ field, fieldState }) => (
            <KanbanSelect
              label='Authentication Type'
              data={authOptions}
              {...field}
              error={fieldState.error?.message}
              required
              disabled={isViewAction}
            />
          )}
        />

        {AuthenticationType.BASIC === authType && (
          <>
            <Controller
              name='username'
              rules={{
                required: 'Username is required',
                validate: (value) => {
                  if (!value?.trim()) {
                    return 'Username is required';
                  }
                  return true;
                },
              }}
              control={control}
              render={({ field, fieldState }) => (
                <KanbanInput
                  label='Username'
                  {...field}
                  error={fieldState.error?.message}
                  required
                  disabled={isViewAction}
                  maxLength={2000}
                  onBlur={(e) => {
                    const value = e.target.value;
                    field.onChange(value.trim());
                  }}
                />
              )}
            />
            <Controller
              name='password'
              control={control}
              rules={{
                required: 'Password is required',
                validate: (value) => {
                  if (!value?.trim()) {
                    return 'Password is required';
                  }
                  return true;
                },
              }}
              render={({ field, fieldState }) => (
                <PasswordInput
                  label='Password'
                  {...field}
                  error={fieldState.error?.message}
                  required
                  disabled={isViewAction}
                  maxLength={2000}
                  onBlur={(e) => {
                    const value = e.target.value;
                    field.onChange(value.trim());
                  }}
                />
              )}
            />
          </>
        )}

        {AuthenticationType.API_TOKEN === authType && (
          <Controller
            rules={{
              required: 'Token key is required',
              validate: (value) => {
                if (!value?.trim()) {
                  return 'Token is required';
                }
                return true;
              },
            }}
            name='tokenKey'
            control={control}
            render={({ field, fieldState }) => (
              <PasswordInput
                label='Token Key'
                {...field}
                error={fieldState.error?.message}
                required
                disabled={isViewAction}
                maxLength={2000}
                onBlur={(e) => {
                  const value = e.target.value;
                  field.onChange(value.trim());
                }}
              />
            )}
          />
        )}

        <Flex justify='space-between' align='center'>
          <KanbanTitle order={5}>Parameters</KanbanTitle>
          <ActionIcon variant='default' radius='xl' onClick={() => append({ type: ParamType.URL, name: '', value: '' })} disabled={isViewAction}>
            <IconPlus size={20} />
          </ActionIcon>
        </Flex>

        {fields.map((field, index) => (
          <Group key={field.id} grow>
            <Controller
              name={`params.${index}.type`}
              control={control}
              render={({ field }) => <KanbanSelect label='Type' data={paramTypeOptions} {...field} disabled={isViewAction} />}
            />
            <Controller
              name={`params.${index}.name`}
              control={control}
              render={({ field }) => (
                <KanbanInput
                  label='Name'
                  {...field}
                  disabled={isViewAction}
                  maxLength={2000}
                  onBlur={(e) => {
                    const value = e.target.value;
                    field.onChange(value.trim());
                  }}
                />
              )}
            />
            <Controller
              name={`params.${index}.value`}
              control={control}
              render={({ field }) => (
                <KanbanInput
                  label='Value'
                  {...field}
                  disabled={isViewAction}
                  maxLength={2000}
                  onBlur={(e) => {
                    const value = e.target.value;
                    field.onChange(value.trim());
                  }}
                />
              )}
            />
            <ActionIcon disabled={isViewAction} variant='light' color='red' radius='xl' onClick={() => remove(index)} className={styles.removeButton}>
              <IconMinus size={16} />
            </ActionIcon>
          </Group>
        ))}
      </Stack>
    </Card>
  );
});

ApiDetails.displayName = 'ApiDetails';
export default ApiDetails;
