import { IntegrateMethodEnum as Method } from '@common/constants/DiscoverySourceConfigEnum';
import { DiscoverySourceTypeEnum } from './DiscoverySourceTypeEnum';
export const DiscoverySourceMethodMap: Record<Method, DiscoverySourceTypeEnum[]> = {
  [Method.API]: [
    DiscoverySourceTypeEnum.DISCOVER_SOURCE_RANCHER_K8S_PROJECT,
    DiscoverySourceTypeEnum.DISCOVER_SOURCE_RANCHER_TANZU_NODE,
    DiscoverySourceTypeEnum.DISCOVER_SOURCE_RANCHER_TANZU_CLUSTERS,
    DiscoverySourceTypeEnum.DISCOVER_SOURCE_NETBOX_VIRTUALIZATION_VIRTUAL_MACHINE,
    DiscoverySourceTypeEnum.DISCOVER_SOURCE_NETBOX_VIRTUALIZATION_VIRTUAL_DISK,
    DiscoverySourceTypeEnum.DISCOVER_SOURCE_NETBOX_DICM_DEVICE,
    DiscoverySourceTypeEnum.DISCOVER_SOURCE_NETBOX_VIRTUALIZATION_CLUSTER,
    DiscoverySourceTypeEnum.DISCOVER_SOURCE_NETBOX_DICM_DEVICE_ROLE_SERVER,
    DiscoverySourceTypeEnum.DISCOVERY_SOURCE_AIX_HMC_LOGICAL_PARTITION,
    DiscoverySourceTypeEnum.DISCOVERY_SOURCE_VROPS_CLUSTERS,
    DiscoverySourceTypeEnum.DISCOVERY_SOURCE_VROPS_RESOURCES,
    DiscoverySourceTypeEnum.DISCOVERY_SOURCE_ACTIVE_IQ_STORAGE_VOLUMES,
  ],
  [Method.SNMP]: [
    DiscoverySourceTypeEnum.DISCOVERY_SOURCE_SNMP_NETWORK_CHECKPOINT,
    DiscoverySourceTypeEnum.DISCOVERY_SOURCE_SNMP_NETWORK_PALOALTO,
    DiscoverySourceTypeEnum.DISCOVERY_SOURCE_SNMP_F5,
  ],
  [Method.DB_CONNECT]: [DiscoverySourceTypeEnum.DISCOVERY_SOURCE_SOLARWIND_NETWORK_USER_DB],
};
