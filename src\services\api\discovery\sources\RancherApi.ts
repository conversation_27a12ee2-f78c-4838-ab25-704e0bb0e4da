import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import { JSONNode } from '@pages/admins/discovery/jsonTransform/helper/JsonTransformHelper';

export class RancherApi extends BaseApi {
  static baseUrl = BaseUrl.ranchers;

  static findAllNodes(sourceId: number) {
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/nodes?sourceId=${sourceId}`);
  }
  static findAllClusters(sourceId: number) {
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/clusters?sourceId=${sourceId}`);
  }
}
