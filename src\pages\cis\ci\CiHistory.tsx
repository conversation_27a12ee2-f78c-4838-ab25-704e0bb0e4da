import { ConfigItemApi } from '@api/ConfigItemApi';
import { CiHistoryAction, CiHistoryModel } from '@models/CiHistory';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { ConfigItemTypeApi } from '@api/ConfigItemTypeApi';
import { ConfigItemTypeAttrResponse } from '@api/ConfigItemTypeAttrApi';
import CiHistoryNoticeChange from './history/CiHistoryNoticeChange';
import { ColumnType, KanbanButton, KanbanTable, KanbanTableProps, renderDateTime, TableAffactedSafeType } from 'kanban-design-system';
import { tableAffectedToPaginationRequestModel } from '@common/utils/KanbanTableUtils';
import equal from 'fast-deep-equal';
import { safetyParseObjectFromJson } from '@common/utils/Helpers';

import { DataCiCompare } from '@models/CiDetailCompare';
import { CiTypeAttributeDataType } from '@models/CiType';

export type CiHistoryProps = {
  ciId: number;
  ciTypeId: number;
};

const handleConvertChange = (data?: string): DataCiCompare | undefined => {
  if (!data) {
    return undefined;
  }
  return safetyParseObjectFromJson(data) as DataCiCompare | undefined;
};

const renderActionHistory = (ciHistory: CiHistoryModel) => {
  const defaultValue = 'black';

  let color: string;
  switch (ciHistory.action) {
    case CiHistoryAction.ADDED:
      color = 'green';
      break;
    case CiHistoryAction.EDITED:
      color = 'orange';
      break;
    case CiHistoryAction.IMPORT:
      color = 'green';
      break;
    case CiHistoryAction.REMOVED:
      color = 'red';
      break;
    default:
      color = defaultValue;
      break;
  }
  return (
    <KanbanButton size='compact-xs' variant={'subtle'} c={'white'} fw={'700'} bg={color}>
      {ciHistory.action}
    </KanbanButton>
  );
};
export const CiHistory = ({ ciId, ciTypeId }: CiHistoryProps) => {
  const [ciHistories, setCiHistories] = useState<CiHistoryModel[]>([]);
  const [tableAffected, setTableAffected] = useState<TableAffactedSafeType | undefined>(undefined);
  const [totalRecords, setTotalRecords] = useState(0);

  const [listCiTypeAttribute, setListCiTypeAttribute] = useState<ConfigItemTypeAttrResponse[]>([]);
  const [currentCiTypeExistsAttributeReference, setCurrentCiTypeExistsAttributeReference] = useState<boolean>(false);
  const [initialLoadDone, setInitialLoadDone] = useState(false);

  const fetchHistoryByPage = useCallback(() => {
    if (!tableAffected) {
      return;
    }
    const pagination = tableAffectedToPaginationRequestModel(tableAffected);
    ConfigItemApi.getAllHistoriesByPaging(ciId, pagination)
      .then((res) => {
        setTotalRecords(res.data.totalElements);
        setCiHistories(res.data.content);
      })
      .catch(() => {})
      .finally(() => {});
  }, [ciId, tableAffected]);
  useEffect(() => {
    fetchHistoryByPage();
  }, [fetchHistoryByPage]);

  const fetchListCiTypeAttributes = useCallback(() => {
    ConfigItemTypeApi.getAllAttributes(ciTypeId)
      .then((res) => {
        const attributes = res.data;
        const hasReferenceType = attributes.some((attr) => CiTypeAttributeDataType.REFERENCE === attr.type);

        setListCiTypeAttribute(attributes);
        setCurrentCiTypeExistsAttributeReference(hasReferenceType);
      })
      .catch(() => {});
  }, [ciTypeId]);

  useEffect(() => {
    fetchListCiTypeAttributes();
  }, [fetchListCiTypeAttributes]);

  // TODO: check logic history, build history save specific
  const updateListCiTypeAttribute = useCallback(() => {
    if (currentCiTypeExistsAttributeReference && !initialLoadDone) {
      let ciTypeAttributesUpdate = [...listCiTypeAttribute];
      const listReferenceIds = new Set(
        ciTypeAttributesUpdate
          .filter((x) => CiTypeAttributeDataType.REFERENCE === x.type && !!x.ciTypeReferenceId)
          .map((item) => item.ciTypeReferenceId ?? -1),
      );

      if (listReferenceIds && listReferenceIds.size > 0) {
        ConfigItemTypeApi.getAllReferAttributeValueSuggestion(ciTypeId)
          .then((res) => {
            if (res.data && res.data.length > 0) {
              const listSuggestReferFields = res.data;

              listReferenceIds.forEach((value) => {
                const listOptions = listSuggestReferFields.filter((x) => x.ciTypeReferenceId === value);
                ciTypeAttributesUpdate = ciTypeAttributesUpdate.map((obj) => {
                  return obj.ciTypeReferenceId === value ? { ...obj, ciTypeReferenceData: listOptions } : obj;
                });
              });
              setListCiTypeAttribute(ciTypeAttributesUpdate);
            }
          })
          .catch(() => {});
      }
      setInitialLoadDone(true);
    }
  }, [currentCiTypeExistsAttributeReference, initialLoadDone, listCiTypeAttribute, ciTypeId]);

  useEffect(() => {
    updateListCiTypeAttribute();
  }, [updateListCiTypeAttribute]);

  const renderCiHistoryChange = useCallback(
    (rowData: CiHistoryModel) => {
      const differentObj = handleConvertChange(rowData.dataChange);
      return (
        listCiTypeAttribute &&
        rowData.dataChange &&
        (differentObj ? (
          <CiHistoryNoticeChange differenceObject={differentObj} listCiTypeAttribute={listCiTypeAttribute} ciHistory={rowData} />
        ) : (
          rowData.dataChange
        ))
      );
    },
    [listCiTypeAttribute],
  );

  const columns: ColumnType<CiHistoryModel>[] = useMemo(() => {
    return [
      {
        title: 'Id',
        name: 'id',
        hidden: true,
        width: '5%',
      },
      {
        title: 'At',
        name: 'createdDate',
        customRender: renderDateTime,
        width: '10%',
      },
      {
        title: 'Approved by',
        name: 'createdBy',
        width: '10%',
      },
      {
        title: 'Requested by',
        name: 'requester',
        width: '10%',
      },
      {
        title: 'Action',
        name: 'action',
        width: '10%',
        customRender: (_, rowData) => {
          return renderActionHistory(rowData);
        },
      },
      {
        title: 'CI data & relationship changes',
        name: 'dataChange',
        customRender: (_, rowData) => {
          return renderCiHistoryChange(rowData);
        },
        width: '70%',
        sortable: false,
      },
    ] as ColumnType<CiHistoryModel>[];
  }, [renderCiHistoryChange]);
  const tableProps: KanbanTableProps<CiHistoryModel> = useMemo(() => {
    return {
      title: 'History',
      showNumericalOrderColumn: true,
      searchable: {
        enable: true,
        debounceTime: 900,
      },
      sortable: {
        enable: true,
      },

      columns: columns,
      data: ciHistories,

      pagination: {
        enable: true,
      },

      serverside: {
        totalRows: totalRecords,
        onTableAffected(dataSet) {
          if (!equal(tableAffected, dataSet)) {
            setTableAffected(dataSet);
          }
        },
      },
    };
  }, [ciHistories, columns, tableAffected, totalRecords]);

  return (
    <>
      <KanbanTable {...tableProps}></KanbanTable>
    </>
  );
};
export default CiHistory;
