import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { ColumnType, KanbanButton, KanbanConfirmModal, KanbanSwitch, useKanbanModals, type TableAffactedSafeType } from 'kanban-design-system';
import { KanbanTable, type KanbanTableProps } from 'kanban-design-system';
import { IconEdit, IconPlus, IconTrash } from '@tabler/icons-react';
import { KanbanIconButton } from 'kanban-design-system';
import { tableAffectedToMultiColumnFilterPaginationRequestModel } from '@common/utils/KanbanTableUtils';
import equal from 'fast-deep-equal';
import { buildJobDiscoveryConfigUrl, buildSourceDataUrl, createOrUpdateDataSourcePath } from '@common/utils/RouterUtils';
import { useNavigate } from 'react-router-dom';
import { BreadcrumbComponent } from '../breadcrumb/BreadcrumbComponent';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { JobDiscoveryConfigApi } from '@api/discovery/JobDiscoveryConfigApi';
import { JobDiscoveryModel } from '@models/JobDiscoveryConfig';
import { getTextJobTypeEnum } from '@common/constants/JobTypeEnum';
import { dateToString, DD_MM_YYYY_HH_MM_SS_FORMAT } from '@common/utils/DateUtils';
import { getTextInterval, TimeUnitEnum } from '@common/constants/TimeUnitEnum';
import { JobGroupEnum } from '@common/constants/JobGroupEnum';
import { NotificationError, NotificationSuccess } from '@common/utils/NotificationUtils';
import { isCurrentUserMatchPermissions } from '@common/utils/AclPermissionUtils';
import { AclPermission } from '@models/AclPermission';
import { JobStatusEnum } from '@common/constants/JobStatusEnum';
import { JobDiscoveryAction } from '@common/constants/JobDiscoveryActionEnum';
import { SourceDataAction } from '@common/constants/SourceDataActionEnum';
import { validateEndDate } from './CreateOrUpdateJobDiscoveryConfig';
import { MAX_NUMBER_LENGTH, MAX_TEXT_LENGTH } from '@common/constants/FieldLengthConstants';
import { useDisclosure } from '@mantine/hooks';

export const JobConfigListPage: React.FC = () => {
  const navigate = useNavigate();
  const [totalRecords, setTotalRecords] = useState(0);
  const [tableAffected, setTableAffected] = useState<TableAffactedSafeType>();
  const [listJob, setListJob] = useState<JobDiscoveryModel[]>([]);
  const [modalConfirmDelete, { close: closeModalConfirmDelete, open: openModalConfirmDelete }] = useDisclosure(false);
  const [deleteId, setDeleteId] = useState<number>(0);

  const fetchListDcis = useCallback(() => {
    if (!tableAffected) {
      return;
    }

    const dataSend = tableAffectedToMultiColumnFilterPaginationRequestModel<JobDiscoveryModel>(
      tableAffected.sortedBy ? tableAffected : { ...tableAffected, sortedBy: 'createdDate', isReverse: true },
    );
    JobDiscoveryConfigApi.getAllJobByGroup(JobGroupEnum.DISCOVERY, dataSend)
      .then((res) => {
        if (res.data) {
          const data = res.data;
          if (data) {
            setListJob(data.content);
            setTotalRecords(data.totalElements);
          }
        }
      })
      .catch(() => {});
  }, [tableAffected]);

  const deleteRow = useCallback(
    (id: number) => {
      JobDiscoveryConfigApi.deleteById(id)
        .then((res) => {
          if (res.data) {
            NotificationSuccess({
              message: 'Deleted successfully',
            });
            fetchListDcis();
          }
        })
        .catch(() => {});
    },
    [fetchListDcis],
  );

  const columns: ColumnType<JobDiscoveryModel>[] = useMemo(() => {
    const cols: ColumnType<JobDiscoveryModel>[] = [
      {
        title: 'ID',
        name: 'id',
        width: '10%',
        hidden: true,
        advancedFilter: {
          variant: 'number',
          filterModes: ['equals', 'notEquals', 'greaterThan', 'greaterThanOrEqualTo', 'lessThan', 'lessThanOrEqualTo'],
          customProps: { maxLength: MAX_NUMBER_LENGTH },
        },
      },
      {
        title: 'Schedule Name',
        name: 'jobName',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
      },
      {
        title: 'Data Source',
        name: 'dataSourceName',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
        customRender: (_, rowData: JobDiscoveryModel) => {
          return (
            <KanbanButton
              size='compact-xs'
              variant={'subtle'}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                navigate(buildSourceDataUrl(rowData.configId, createOrUpdateDataSourcePath, SourceDataAction.VIEW));
              }}>
              {rowData.dataSourceName}
            </KanbanButton>
          );
        },
      },
      {
        title: 'Type',
        name: 'jobType',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
        customRender: (_, rowData: JobDiscoveryModel) => {
          return getTextJobTypeEnum(rowData.jobType);
        },
      },
      {
        title: 'Status',
        name: 'status',
        width: '10%',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
        customRender: (_: string, rowData: JobDiscoveryModel) => {
          return (
            <KanbanButton size='compact-xs' variant={'subtle'} c={'white'} fw={'700'} bg={rowData.status === 'ACTIVE' ? 'green' : 'red'}>
              {rowData.status}
            </KanbanButton>
          );
        },
      },

      {
        title: 'Updated',
        name: 'modifiedDate',
        hidden: true,
        advancedFilter: {
          variant: 'date',
          customProps: {
            popoverProps: {
              withinPortal: false,
            },
          },
        },
        customRender: (_, rowData: JobDiscoveryModel) => {
          return rowData.modifiedDate ? dateToString(rowData.modifiedDate, DD_MM_YYYY_HH_MM_SS_FORMAT) : '';
        },
      },
      {
        title: 'Start',
        name: 'startDate',
        advancedFilter: {
          variant: 'date',
          customProps: {
            popoverProps: {
              withinPortal: false,
            },
          },
        },
        customRender: (_, rowData: JobDiscoveryModel) => {
          return rowData.startDate ? dateToString(rowData.startDate, DD_MM_YYYY_HH_MM_SS_FORMAT) : '';
        },
      },
      {
        title: 'End date',
        name: 'endDate',
        advancedFilter: {
          variant: 'date',
          customProps: {
            popoverProps: {
              withinPortal: false,
            },
          },
        },
        customRender: (_, rowData: JobDiscoveryModel) => {
          return rowData.endDate ? dateToString(rowData.endDate, DD_MM_YYYY_HH_MM_SS_FORMAT) : '';
        },
      },

      {
        title: 'Interval',
        name: 'intervalTime',
        width: '10%',
        advancedFilter: {
          variant: 'number',
          filterModes: ['equals', 'notEquals', 'greaterThan', 'greaterThanOrEqualTo', 'lessThan', 'lessThanOrEqualTo'],
          customProps: { maxLength: MAX_NUMBER_LENGTH },
        },
        customRender: (_, rowData: JobDiscoveryModel) => {
          return getTextInterval(rowData.intervalTime, rowData.frequency);
        },
      },
      {
        title: 'Next Run',
        name: 'nextRun',
        advancedFilter: {
          enable: false,
        },
        sortable: false,
        customRender: (_, rowData: JobDiscoveryModel) => {
          const { endDate, frequency, nextRun, status } = rowData;

          const isOnceTime = frequency === TimeUnitEnum.ONCE_TIME;
          const isInactive = status === JobStatusEnum.INACTIVE;
          const isEnded = endDate && nextRun && new Date(endDate) < new Date(nextRun);

          if (isOnceTime || isInactive || isEnded) {
            return '';
          }

          return nextRun ? dateToString(nextRun, DD_MM_YYYY_HH_MM_SS_FORMAT) : '';
        },
      },
      {
        title: 'Created by',
        name: 'createdBy',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
      },
      {
        title: 'Last modified by',
        hidden: true,
        name: 'modifiedBy',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
      },
    ];

    return cols;
  }, [navigate]);

  const changeStatusScheduleJob = useCallback((data: JobDiscoveryModel) => {
    const validateData = validateEndDate(data);
    if (validateData) {
      NotificationError({
        message: validateData,
      });

      return validateData;
    }
    JobDiscoveryConfigApi.save(data)
      .then((res) => {
        if (res.data) {
          NotificationSuccess({ title: 'Success', message: 'Change status schedule job successfully' });
        }
      })
      .catch(() => {});
  }, []);

  const modelProvider = useKanbanModals();

  const tableProps: KanbanTableProps<JobDiscoveryModel> = useMemo(() => {
    const cols = columns;

    return {
      maxHeight: '100%',
      showNumericalOrderColumn: true,
      searchable: {
        enable: true,
        debounceTime: 300,
      },
      sortable: {
        enable: true,
      },
      advancedFilterable: {
        enable: true,
        debounceTime: 1000,
        resetOnClose: true,
        compactMode: true,
      },
      onRowClicked: (data: JobDiscoveryModel) => {
        navigate(buildJobDiscoveryConfigUrl(data.id || 0, JobDiscoveryAction.UPDATE));
      },
      columns: cols,
      data: listJob,
      key: cols,
      serverside: {
        totalRows: totalRecords,
        onTableAffected(dataSet) {
          if (!equal(tableAffected, dataSet)) {
            setTableAffected(dataSet);
          }
        },
      },
      pagination: {
        enable: true,
      },
      actions: {
        customAction: isCurrentUserMatchPermissions(AclPermission.actionTableJobConfigPermissions)
          ? (data: JobDiscoveryModel) => {
              return (
                <>
                  {isCurrentUserMatchPermissions([AclPermission.updateJobConfig]) && !data.systemJob && (
                    <KanbanSwitch
                      size='xs'
                      mr='xs'
                      color={'green'}
                      checked={JobStatusEnum.ACTIVE === data.status}
                      onClick={() => {
                        const actionStatus = data.status === JobStatusEnum.ACTIVE ? JobStatusEnum.INACTIVE : JobStatusEnum.ACTIVE;
                        const idModelStatus = modelProvider.openConfirmModal({
                          title: `Confirm ${actionStatus.toLowerCase()} schedule jobs`,
                          children: `Are you sure to ${actionStatus.toLowerCase()} these item(s)?`,
                          onConfirm: () => {
                            data.status = actionStatus;
                            if (changeStatusScheduleJob(data)) {
                              data.status = actionStatus === JobStatusEnum.INACTIVE ? JobStatusEnum.ACTIVE : JobStatusEnum.INACTIVE;
                            }
                            modelProvider.closeModal(idModelStatus);
                          },
                        });
                      }}
                    />
                  )}
                  {isCurrentUserMatchPermissions([AclPermission.updateJobConfig]) && (
                    <KanbanIconButton
                      variant='transparent'
                      size={'sm'}
                      onClick={() => {
                        navigate(buildJobDiscoveryConfigUrl(data.id || 0, JobDiscoveryAction.UPDATE));
                      }}>
                      <IconEdit />
                    </KanbanIconButton>
                  )}
                  {isCurrentUserMatchPermissions([AclPermission.deleteJobConfig]) && !data.systemJob && (
                    <KanbanIconButton
                      color='red'
                      size='sm'
                      variant='transparent'
                      onClick={() => {
                        setDeleteId(data.id || 0);
                        openModalConfirmDelete();
                      }}>
                      <IconTrash />
                    </KanbanIconButton>
                  )}
                </>
              );
            }
          : undefined,
      },
    };
  }, [columns, listJob, totalRecords, navigate, tableAffected, modelProvider, changeStatusScheduleJob, openModalConfirmDelete]);

  useEffect(() => {
    fetchListDcis();
  }, [fetchListDcis]);

  return (
    <>
      {/* Modal confirm delete  */}
      <KanbanConfirmModal
        opened={modalConfirmDelete}
        onClose={closeModalConfirmDelete}
        title='Confirm delete'
        onConfirm={() => {
          deleteRow(deleteId);
          closeModalConfirmDelete();
        }}>
        Are you sure to delete this item?
      </KanbanConfirmModal>

      <BreadcrumbComponent />
      <HeaderTitleComponent
        title='Scheduled Jobs'
        rightSection={
          <>
            {isCurrentUserMatchPermissions([AclPermission.createJobConfig]) && (
              <KanbanButton onClick={() => navigate(buildJobDiscoveryConfigUrl(0, JobDiscoveryAction.CREATE))} leftSection={<IconPlus />}>
                New Schedule Job
              </KanbanButton>
            )}
          </>
        }
      />
      <KanbanTable {...tableProps} />
    </>
  );
};
