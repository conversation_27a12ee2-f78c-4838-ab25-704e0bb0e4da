import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { KanbanButton, KanbanSwitch, KanbanText, KanbanTooltip, type TableAffactedSafeType } from 'kanban-design-system';
import { KanbanTable, type ColumnType, type KanbanTableProps } from 'kanban-design-system';
import { renderDateTime } from 'kanban-design-system';
import { IconCopy, IconPlus, IconTrash } from '@tabler/icons-react';
import { KanbanConfirmModal } from 'kanban-design-system';
import { useDisclosure } from '@mantine/hooks';
import { KanbanIconButton } from 'kanban-design-system';
import equal from 'fast-deep-equal';
import { Flex } from '@mantine/core';
import { IconEye } from '@tabler/icons-react';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { PaginationRequestModel } from '@models/EntityModelBase';
import { IconEdit } from '@tabler/icons-react';
import { ImpactedRuleApi } from '@api/ImpactedRuleApi';
import { NotificationSuccess } from '@common/utils/NotificationUtils';
import styled from './ImpactedRule.module.scss';
import { useNavigate } from 'react-router-dom';
import { buildImpactedRuleDetailUrl } from '@common/utils/RouterUtils';
import { isCurrentUserMatchPermissions } from '@common/utils/AclPermissionUtils';
import { AclPermission } from '@models/AclPermission';
import GuardComponent from '@components/GuardComponent';
import { CiRelationshipDirectionEnum, ImpactedRuleModel, RuleAction } from '@models/ImpactedRule';
import { useGetRelationshipTypes } from '@slices/CiRelationshipTypesSlice';
import { BreadcrumbComponent } from '@pages/admins/breadcrumb/BreadcrumbComponent';
import { tableAffectedToMultiColumnFilterPaginationRequestModel } from '@common/utils/KanbanTableUtils';
import { MAX_NUMBER_LENGTH, MAX_TEXT_LENGTH } from '@common/constants/FieldLengthConstants';

const renderDirectRelationship = (data?: string, direction?: CiRelationshipDirectionEnum) => {
  if (!data || !direction) {
    return <></>;
  }
  return (
    <KanbanTooltip label={data}>
      <KanbanButton
        size='compact-xs'
        variant={'subtle'}
        c={'white'}
        fw={'700'}
        bg={CiRelationshipDirectionEnum.IN === direction ? 'cyan' : 'primary'}>
        {data}
      </KanbanButton>
    </KanbanTooltip>
  );
};
export const ImpactedRulesPage = () => {
  const navigate = useNavigate();
  const [listRule, setListRule] = useState<ImpactedRuleModel[]>([]);
  const allRelationships = useGetRelationshipTypes().data;
  const [openedConfirmModal, { close: closeConfirmModal, open: openConfirmModal }] = useDisclosure(false);
  const [currentRule, setCurrentRule] = useState<ImpactedRuleModel>();
  const [totalRecords, setTotalRecords] = useState(0);
  const [tableAffected, setTableAffected] = useState<TableAffactedSafeType>();

  const [actionOnRule, setActionOnRule] = useState<RuleAction>(RuleAction.VIEW);

  // const currentUser = useSelector(getCurrentUser);
  // const isSuperAdmin = currentUser.data?.isSuperAdmin === true;
  const renderConfirmModalTitle = useCallback(() => {
    switch (actionOnRule) {
      case RuleAction.DELETE:
        return <>Confirm delete item</>;
      case RuleAction.CHANGE_STATUS:
        return <>Confirm change item status</>;
      default:
        return <>Action unknown</>;
    }
  }, [actionOnRule]);
  //051224 cai tien confirm rule popup
  const renderConfirmModalContent = useCallback(() => {
    switch (actionOnRule) {
      case RuleAction.DELETE:
        return (
          <KanbanText>
            Are you sure to delete <b>{currentRule?.ruleName}</b>?
          </KanbanText>
        );
      case RuleAction.CHANGE_STATUS:
        return (
          <KanbanText>
            Are you sure to {currentRule?.active ? 'inactivate' : 'activate'} <b>{currentRule?.ruleName}</b>?
          </KanbanText>
        );
      default:
        return <></>;
    }
  }, [actionOnRule, currentRule?.active, currentRule?.ruleName]);
  const fetchListRule = useCallback(() => {
    if (!tableAffected) {
      return;
    }
    const dataSend: PaginationRequestModel<ImpactedRuleModel> = tableAffectedToMultiColumnFilterPaginationRequestModel<ImpactedRuleModel>(
      tableAffected.sortedBy ? tableAffected : { ...tableAffected, sortedBy: 'createdDate', isReverse: true },
    );

    ImpactedRuleApi.fetchRuleImpacteds(dataSend)
      .then((res) => {
        if (res.data) {
          const datas = res.data.content;
          setListRule(datas);
          setTotalRecords(res.data.totalElements);
        }
      })
      .catch(() => {});

    // setListRule(sampleDataRules);
  }, [tableAffected]);
  const handleDeleteRules = useCallback(
    (rows: ImpactedRuleModel[]) => {
      setActionOnRule(RuleAction.DELETE);

      ImpactedRuleApi.deleteRuleImpacteds(rows.map((it) => it.id)).then(() => {
        NotificationSuccess({ message: 'Delete rules successfully' });
        fetchListRule();
      });
    },
    [fetchListRule],
  );

  const columns: ColumnType<ImpactedRuleModel>[] = useMemo(() => {
    const cols: ColumnType<ImpactedRuleModel>[] = [
      {
        title: 'Impact Rule Name',
        sortable: true,
        name: 'ruleName',
        width: '20%',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
        customRender: (data) => {
          return (
            <KanbanTooltip label={data}>
              <KanbanText className={styled.clipText}>{data}</KanbanText>
            </KanbanTooltip>
          );
        },
      },
      {
        title: 'Description',
        sortable: true,
        name: 'description',
        width: '20%',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
        customRender: (data) => {
          return (
            <KanbanTooltip label={data} multiline w={'500'}>
              <KanbanText className={styled.clipText}>{data}</KanbanText>
            </KanbanTooltip>
          );
        },
      },

      {
        title: 'Relationship type',
        sortable: true,
        name: 'relationshipTypeName',
        width: '20%',
        advancedFilter: {
          enable: false,
        },
        customRender: (data, rowData) => {
          const rel = allRelationships.find((it) => it.id === rowData.relationshipTypeId);
          return (
            <>
              {renderDirectRelationship(rel?.type, CiRelationshipDirectionEnum.OUT)}
              {` - `}
              {renderDirectRelationship(rel?.inverseType, CiRelationshipDirectionEnum.IN)}
            </>
          );
        },
      },
      {
        title: 'Impact relationship',
        sortable: true,
        name: 'relationshipDirectionImpacted',
        width: '20%',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
        customRender: (data, rowData) => {
          return <>{renderDirectRelationship(data, rowData.relationshipDirection as CiRelationshipDirectionEnum)}</>;
        },
      },
      {
        title: 'Created by',
        sortable: true,
        name: 'createdBy',
        width: '15%',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
      },
      {
        title: 'Created date',
        sortable: true,
        name: 'createdDate',
        customRender: renderDateTime,
        width: '15%',
        advancedFilter: {
          variant: 'date',
          customProps: {
            popoverProps: {
              withinPortal: false,
            },
          },
        },
      },
      {
        title: 'Modified by',
        sortable: true,
        name: 'modifiedBy',
        width: '10%',
        advancedFilter: {
          variant: 'text',
          customProps: { maxLength: MAX_TEXT_LENGTH },
        },
      },
      {
        title: 'Modified date',
        sortable: true,
        name: 'modifiedDate',
        customRender: renderDateTime,
        width: '10%',
        advancedFilter: {
          variant: 'date',
          customProps: {
            popoverProps: {
              withinPortal: false,
            },
          },
        },
      },
      {
        title: 'Status',
        sortable: true,
        name: 'active',
        customRender: (data) => {
          return (
            <KanbanButton size='compact-xs' variant={'subtle'} c={'white'} fw={'700'} bg={data ? 'green' : 'red'}>
              {data ? 'Active' : 'Inactive'}
            </KanbanButton>
          );
        },
        width: '10%',
        advancedFilter: {
          variant: 'number',
          filterModes: ['equals', 'notEquals', 'greaterThan', 'greaterThanOrEqualTo', 'lessThan', 'lessThanOrEqualTo'],
          customProps: { maxLength: MAX_NUMBER_LENGTH },
        },
      },
    ];

    return cols;
  }, [allRelationships]);
  const handleChangeStatus = useCallback(
    (rowData: ImpactedRuleModel, data: boolean) => {
      ImpactedRuleApi.updateRuleImpactedStatus(rowData.id, data)
        .then(() => {
          const updateLst = listRule.map((it) => {
            if (it.id === rowData.id) {
              return { ...it, active: data };
            }
            return it;
          });
          setListRule(updateLst);
          NotificationSuccess({ message: 'Update status successfully' });
        })
        .catch(() => {});
    },
    [listRule],
  );

  const tableProps: KanbanTableProps<ImpactedRuleModel> = useMemo(() => {
    const cols = columns;
    return {
      showNumericalOrderColumn: true,
      searchable: {
        enable: true,
        debounceTime: 1000,
      },
      sortable: {
        enable: true,
      },
      advancedFilterable: {
        enable: true,
        debounceTime: 1000,
        resetOnClose: true,
        compactMode: true,
      },
      columns: cols,
      data: listRule,
      //set key to smth that change when ever change 'Select option to view' -> trigger table reload to cols
      key: cols,
      serverside: {
        totalRows: totalRecords,
        onTableAffected(dataSet) {
          if (!equal(tableAffected, dataSet)) {
            setTableAffected(dataSet);
          }
        },
      },
      pagination: {
        enable: true,
      },
      onRowClicked: (data) => {
        if (isCurrentUserMatchPermissions([AclPermission.viewDetailImpactedRule])) {
          navigate(buildImpactedRuleDetailUrl(data.id, RuleAction.VIEW));
        }
      },
      selectableRows: {
        enable: isCurrentUserMatchPermissions([AclPermission.deleteImpactedRule]),
        onDeleted(rows) {
          handleDeleteRules(rows);
        },
      },
      actions: {
        customAction: (rowData) => {
          return (
            <>
              <KanbanTooltip label='Change status rule'>
                <KanbanSwitch
                  size='xs'
                  mr='xs'
                  color={'green'}
                  checked={rowData.active ?? false}
                  onChange={() => {
                    if (isCurrentUserMatchPermissions([AclPermission.updateImpactedRule])) {
                      setCurrentRule(rowData);
                      setActionOnRule(RuleAction.CHANGE_STATUS);
                      openConfirmModal();
                    }
                  }}
                />
              </KanbanTooltip>
              <GuardComponent requirePermissions={[AclPermission.viewDetailImpactedRule]} hiddenOnUnSatisfy>
                <KanbanTooltip label='View rule'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      // onOpenRuleDetailModal(RuleAction.VIEW, rowData);
                      navigate(buildImpactedRuleDetailUrl(rowData.id, RuleAction.VIEW));
                    }}>
                    <IconEye />
                  </KanbanIconButton>
                </KanbanTooltip>
              </GuardComponent>
              <GuardComponent requirePermissions={[AclPermission.updateImpactedRule]} hiddenOnUnSatisfy>
                <KanbanTooltip label='Edit rule'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      navigate(buildImpactedRuleDetailUrl(rowData.id, RuleAction.EDIT));
                    }}>
                    <IconEdit />
                  </KanbanIconButton>
                </KanbanTooltip>
              </GuardComponent>
              <GuardComponent requirePermissions={[AclPermission.createImpactedRule]} hiddenOnUnSatisfy>
                <KanbanTooltip label='Copy rule'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      // onOpenRuleDetailModal(RuleAction.COPY, rowData);
                      navigate(buildImpactedRuleDetailUrl(rowData.id, RuleAction.COPY));
                    }}>
                    <IconCopy />
                  </KanbanIconButton>
                </KanbanTooltip>
              </GuardComponent>
              <GuardComponent requirePermissions={[AclPermission.deleteImpactedRule]} hiddenOnUnSatisfy>
                <KanbanTooltip label='Delete rule'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    color={'red'}
                    onClick={() => {
                      setCurrentRule(rowData);
                      setActionOnRule(RuleAction.DELETE);
                      openConfirmModal();
                    }}>
                    <IconTrash />
                  </KanbanIconButton>
                </KanbanTooltip>
              </GuardComponent>
            </>
          );
        },
      },
    };
  }, [columns, listRule, totalRecords, tableAffected, navigate, handleDeleteRules, openConfirmModal]);

  const customTableProps = useMemo(() => {
    const tablePropsUpdate = { ...tableProps };
    return tablePropsUpdate;
  }, [tableProps]);

  useEffect(() => {
    fetchListRule();
  }, [fetchListRule]);

  const onConfirmConfirmModal = useCallback(() => {
    switch (actionOnRule) {
      case RuleAction.DELETE:
        ImpactedRuleApi.deleteRuleImpacteds([currentRule?.id ?? 0]).then(() => {
          NotificationSuccess({ message: 'Delete rule successfully' });
          fetchListRule();
        });
        break;
      case RuleAction.CHANGE_STATUS:
        // call api
        if (currentRule) {
          handleChangeStatus(currentRule, !currentRule.active);
        }
        break;
      default:
        break;
    }
    closeConfirmModal();
  }, [actionOnRule, closeConfirmModal, currentRule, fetchListRule, handleChangeStatus]);
  // const synchronizeImpactedCiRuleRelationShip = useCallback(() => {
  //   ImpactedCiRuleRelationApi.synchronizeImpactedCiRuleRelationShip()
  //     .then(() => {
  //       NotificationSuccess({ message: 'Synchronized data to Redis' });
  //     })
  //     .catch(() => {});
  // }, []);
  return (
    <>
      {/* 4763 impacted rule */}
      <BreadcrumbComponent />
      <HeaderTitleComponent
        title='Impacted rules'
        rightSection={
          <Flex justify={'flex-start'} gap={'xs'}>
            <GuardComponent requirePermissions={[AclPermission.createImpactedRule]} hiddenOnUnSatisfy>
              <KanbanButton
                onClick={() => {
                  navigate(buildImpactedRuleDetailUrl(0, RuleAction.ADD));
                }}
                leftSection={<IconPlus />}>
                Add rule
              </KanbanButton>
            </GuardComponent>
            {/* {isSuperAdmin && (
              <KanbanTooltip label={'Re caching data'}>
                <KanbanButton onClick={synchronizeImpactedCiRuleRelationShip} leftSection={<Icon3dCubeSphere />}>
                  Sync
                </KanbanButton>
              </KanbanTooltip>
            )} */}
          </Flex>
        }
      />
      <KanbanConfirmModal
        title={renderConfirmModalTitle()}
        onClose={closeConfirmModal}
        onConfirm={onConfirmConfirmModal}
        textConfirm='Confirm'
        opened={openedConfirmModal}
        modalProps={{
          size: '50%',
        }}>
        {renderConfirmModalContent()}
      </KanbanConfirmModal>

      <KanbanTable {...customTableProps} />
    </>
  );
};
