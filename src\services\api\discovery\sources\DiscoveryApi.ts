import { <PERSON>box<PERSON><PERSON> } from './NetboxApi';
import { <PERSON><PERSON><PERSON><PERSON> } from './RancherApi';
import { ApiResponse } from '@core/api/ApiResponse';
import { DiscoverySourceTypeEnum } from '@common/constants/DiscoverySourceTypeEnum';
import { AixHmc<PERSON><PERSON> } from './AixHmcApi';
import { VropsApi } from './VropsApi';
import { SolarwindA<PERSON> } from './SolarwindApi';
import { SnmpApi } from './SnmpApi';
import { DeviceTypeEnum } from '@common/constants/DeviceTypeEnum';
import { ActiveIqApi } from './ActiveIqApi';
import { K8sApi } from './K8sApi';
import { JSONNode } from '@pages/admins/discovery/jsonTransform/helper/JsonTransformHelper';

export class DiscoveryApi {
  static discoveryData = (sourceType: DiscoverySourceTypeEnum, sourceId: number): Promise<ApiResponse<JSONNode>> => {
    switch (sourceType) {
      case DiscoverySourceTypeEnum.DISCOVER_SOURCE_RANCHER_K8S_PROJECT:
        return K8sApi.discovery(sourceId);
      case DiscoverySourceTypeEnum.DISCOVER_SOURCE_NETBOX_VIRTUALIZATION_VIRTUAL_MACHINE:
        return NetboxApi.findVirtualMachines(sourceId);
      case DiscoverySourceTypeEnum.DISCOVER_SOURCE_NETBOX_VIRTUALIZATION_VIRTUAL_DISK:
        return NetboxApi.findVirtualDisks(sourceId);
      case DiscoverySourceTypeEnum.DISCOVER_SOURCE_NETBOX_VIRTUALIZATION_CLUSTER:
        return NetboxApi.findVirtualClusters(sourceId);
      case DiscoverySourceTypeEnum.DISCOVERY_SOURCE_AIX_HMC_LOGICAL_PARTITION:
        return AixHmcApi.findAllLogicalPartition(sourceId);
      case DiscoverySourceTypeEnum.DISCOVER_SOURCE_NETBOX_DICM_DEVICE:
      case DiscoverySourceTypeEnum.DISCOVER_SOURCE_NETBOX_DICM_DEVICE_ROLE_SERVER:
        return NetboxApi.findDevices(sourceId);
      case DiscoverySourceTypeEnum.DISCOVERY_SOURCE_VROPS_CLUSTERS:
        return VropsApi.findAllClusters(sourceId);
      case DiscoverySourceTypeEnum.DISCOVERY_SOURCE_VROPS_RESOURCES:
        return VropsApi.findAllResources(sourceId);
      case DiscoverySourceTypeEnum.DISCOVER_SOURCE_RANCHER_TANZU_NODE:
        return RancherApi.findAllNodes(sourceId);
      case DiscoverySourceTypeEnum.DISCOVER_SOURCE_RANCHER_TANZU_CLUSTERS:
        return RancherApi.findAllClusters(sourceId);
      case DiscoverySourceTypeEnum.DISCOVERY_SOURCE_SNMP_NETWORK_CHECKPOINT:
        return SnmpApi.findAllDevice(DeviceTypeEnum.CHECKPOINT, sourceId);
      case DiscoverySourceTypeEnum.DISCOVERY_SOURCE_SNMP_NETWORK_PALOALTO:
        return SnmpApi.findAllDevice(DeviceTypeEnum.PALOALTO, sourceId);
      case DiscoverySourceTypeEnum.DISCOVERY_SOURCE_ACTIVE_IQ_STORAGE_VOLUMES:
        return ActiveIqApi.findAllStorageVolumes(sourceId);
      case DiscoverySourceTypeEnum.DISCOVERY_SOURCE_SNMP_F5:
        return SnmpApi.findAllDevice(DeviceTypeEnum.F5, sourceId);
      case DiscoverySourceTypeEnum.DISCOVERY_SOURCE_SOLARWIND_NETWORK_USER_DB:
        return SolarwindApi.findAllByDb(sourceId);
      default:
        throw new Error('Unknown source type');
    }
  };
}
