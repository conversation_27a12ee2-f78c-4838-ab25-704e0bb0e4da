import { buildCiTypeUrl } from '@common/utils/RouterUtils';
import { KanbanButton } from 'kanban-design-system';
import { ColumnType, KanbanTable, KanbanTableProps } from 'kanban-design-system';
import { KanbanText } from 'kanban-design-system';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { CiManagementApi, type CiManagementResponse } from '@api/CiManagementApi';
import { NotificationError, NotificationSuccess } from '@common/utils/NotificationUtils';
import { Flex, Group, Stack, Tooltip } from '@mantine/core';
import { ConfigItemTypeApi } from '@api/ConfigItemTypeApi';
import { configItemTypeAttrSorted, messageApprovalByAction } from '@common/utils/CiUtils';
import type { ConfigItemTypeAttrResponse } from '@api/ConfigItemTypeAttrApi';
import { ActionType } from '@common/constants/CiManagement';
import { KanbanCheckbox } from 'kanban-design-system';
import UserPicker from '@components/commonCi/advanceSearch/UserPicker';
import { KanbanIconButton } from 'kanban-design-system';
import { IconEye } from '@tabler/icons-react';
import { KanbanConfirmModal } from 'kanban-design-system';
import { KanbanTextarea } from 'kanban-design-system';
import { useDisclosure } from '@mantine/hooks';
import type { PaginationRequestModel } from '@models/EntityModelBase';
import type { EntityUserInfoResponse } from '@api/systems/UsersApi';
import CiManagementDetailViewPopup, { CiManagementDetailViewPopupMethods } from './CiManagementDetailViewPopup';
import { useGetCiTypes } from '@slices/CiTypesSlice';
import { klona } from 'klona';
import { CiTypeAttributeDataType } from '@models/CiType';
import { CHANGE_STAGES } from '@common/constants/ChangeAssessmentConstants';
import { PermissionAction } from '@common/constants/PermissionAction';
import { AclPermission } from '@models/AclPermission';
import { isCurrentUserMatchPermissions } from '@common/utils/AclPermissionUtils';

export type CiRequestDetailProps = {
  listData: CiManagementResponse[];
  screenAction: ActionType; // type of screen: send for approval or approvals
  actionType: ActionType; // action for approval: send/approve/reject
  isFromDetail?: boolean;
  onCloseModal?: (val: ActionType) => void;
  onChangeData?: (val: boolean) => void;
  openModalConfirmViewChange?: (val: ActionType) => void;
  setActionType: (val: ActionType) => void;
};

const LIST_ALERT_UPDATE_CI_IMPACT_CHANGE_STAGE = [
  CHANGE_STAGES.SUBMISSION,
  CHANGE_STAGES.PLANNING,
  CHANGE_STAGES.APPROVAL,
  CHANGE_STAGES.IMPLEMENTATION,
];
export const CiRequestDetail = (props: CiRequestDetailProps) => {
  const navigate = useNavigate();

  const { actionType, isFromDetail, listData, onChangeData, openModalConfirmViewChange, screenAction, setActionType } = props;
  const [listCi, setListCi] = useState<CiManagementResponse[]>([]);
  const [selectedIds, setSelectedIds] = useState<number[]>([]);
  const [listIdNotAllowSelect, setListIdNotAllowSelect] = useState<number[]>([]);
  const [listCiTypeAttribute, setListCiTypeAttribute] = useState<ConfigItemTypeAttrResponse[]>([]);
  const [ciTypeAttributes, setCiTypeAttributes] = useState<ConfigItemTypeAttrResponse[]>([]);
  const [currentData, setCurrentData] = useState<CiManagementResponse>();
  const [openedModalConfirmApproval, { close: closeModalConfirmApproval, open: openModalConfirmApproval }] = useDisclosure(false);
  const [openedModalNotiInvalidCis, { close: closeModalNotiInvalidCis, open: openModalNotiInvalidCis }] = useDisclosure(false);

  const [listAlertCi, setListAlertCi] = useState<number[]>();
  const [errorMessage, setErrorMessage] = useState('');
  const [reason, setReason] = useState('');

  const [syncByCiType, setSyncByCiType] = useState(false);
  const [syncByCi, setSyncByCi] = useState(false);
  const [currentCiTypeExistsAttributeReference, setCurrentCiTypeExistsAttributeReference] = useState<boolean>(false);

  const childRefViewDetail = useRef<CiManagementDetailViewPopupMethods | null>(null);

  const handleApproveCis = useCallback(() => {
    CiManagementApi.findChangeInfoByCiTempIds(selectedIds)
      .then((res) => {
        if (!res || !res.data) {
          return;
        }

        const listFlatCiChangeData = res.data;
        const listCiIdAlert = Array.from(
          new Set(
            listFlatCiChangeData
              .filter((it) => {
                return it.changeInfo && LIST_ALERT_UPDATE_CI_IMPACT_CHANGE_STAGE.includes(it.changeInfo.stage as CHANGE_STAGES);
              })
              .map((it) => it.ciId),
          ),
        );

        if (listCiIdAlert && listCiIdAlert.length > 0) {
          setListAlertCi(listCiIdAlert);
          openModalNotiInvalidCis();
          return;
        }
        openModalConfirmApproval();
      })
      .catch(() => {});
  }, [openModalConfirmApproval, openModalNotiInvalidCis, selectedIds]);

  const checkSameCiTypeId = useMemo(() => {
    setListCi(listData);
    const listIds = listData.map((row) => row.id);
    setSelectedIds(listIds);
    const firstCiType = listData[0]?.ciTypeId;

    if (!firstCiType) {
      return null;
    }

    const isSameType = listData.every((obj) => obj.ciTypeId === firstCiType);

    return isSameType ? firstCiType : null;
  }, [listData]);

  const checkPermissionData = useMemo(() => {
    return listData.some((item) =>
      isCurrentUserMatchPermissions(AclPermission.createCiPermissions(PermissionAction.CI__VIEW_ADVANCED, item.ciId, item.ciTypeId), false),
    );
  }, [listData]);

  const fetchCiTypesAttribute = useCallback(() => {
    if (checkSameCiTypeId && checkPermissionData) {
      ConfigItemTypeApi.getAllAttributes(checkSameCiTypeId)
        .then((res) => {
          const ciTypeAttributesResponse = res.data;
          setCiTypeAttributes(ciTypeAttributesResponse);
          setListCiTypeAttribute(configItemTypeAttrSorted(ciTypeAttributesResponse));
          setCurrentCiTypeExistsAttributeReference(ciTypeAttributesResponse.some((x) => CiTypeAttributeDataType.REFERENCE === x.type));
        })
        .catch(() => {});
    } else {
      setListCiTypeAttribute([]);
    }
  }, [checkPermissionData, checkSameCiTypeId]);

  useEffect(() => {
    fetchCiTypesAttribute();
  }, [fetchCiTypesAttribute]);

  const updateListCiTypeAttribute = useCallback(() => {
    if (checkSameCiTypeId && currentCiTypeExistsAttributeReference) {
      let ciTypeAttributesUpdate = [...ciTypeAttributes];
      const listReferenceIds = new Set(
        ciTypeAttributesUpdate
          .filter((x) => CiTypeAttributeDataType.REFERENCE === x.type && !!x.ciTypeReferenceId)
          .map((item) => item.ciTypeReferenceId ?? -1),
      );

      if (listReferenceIds && listReferenceIds.size > 0) {
        ConfigItemTypeApi.getAllReferAttributeValueSuggestion(checkSameCiTypeId)
          .then((res) => {
            if (res.data && res.data.length > 0) {
              const listSuggestReferFields = res.data;

              listReferenceIds.forEach((value) => {
                const listOptions = listSuggestReferFields.filter((x) => x.ciTypeReferenceId === value);
                ciTypeAttributesUpdate = ciTypeAttributesUpdate.map((obj) => {
                  return obj.ciTypeReferenceId === value ? { ...obj, ciTypeReferenceData: listOptions } : obj;
                });
              });
              setListCiTypeAttribute(ciTypeAttributesUpdate);
            }
          })
          .catch(() => {});
      }
    }
  }, [checkSameCiTypeId, currentCiTypeExistsAttributeReference, ciTypeAttributes]);

  useEffect(() => {
    updateListCiTypeAttribute();
  }, [updateListCiTypeAttribute]);

  const allCiTypes = useGetCiTypes();

  const columns: ColumnType<CiManagementResponse>[] = useMemo(() => {
    return [
      {
        name: 'checkbox',
        title: '',
        sortable: false,
        width: '3%',
        customRenderHeader: () => {
          return (
            <KanbanCheckbox
              checked={selectedIds.length === listCi.length - listIdNotAllowSelect.length}
              onChange={(e) => {
                const checked = e.target.checked;
                if (checked) {
                  const listId: number[] = listCi.filter((x) => !listIdNotAllowSelect.includes(x.id)).map((row) => row.id);
                  setSelectedIds(listId);
                } else {
                  setSelectedIds([]);
                }
              }}
            />
          );
        },
        customRender: (_, row) => {
          return (
            <>
              {!listIdNotAllowSelect.includes(row.id) && (
                <KanbanCheckbox
                  checked={selectedIds.some((x) => x === row.id)}
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                  onChange={(e) => {
                    const checked = e.target.checked;
                    let selected = [...selectedIds];
                    if (checked) {
                      selected.push(row.id);
                    } else {
                      selected = selected.filter((x) => x !== row.id);
                    }
                    setSelectedIds(selected);
                  }}
                />
              )}
            </>
          );
        },
      },
      {
        title: 'Id',
        name: 'id',
        hidden: true,
      },
      {
        title: 'Action',
        name: 'action',
        width: '10%',
      },
      {
        title: 'CI Name',
        name: 'name',
        width: '10%',
        customRender: (_data, row) => {
          return <KanbanText lineClamp={2}>{row.dataParse?.ci?.name}</KanbanText>;
        },
      },
      {
        title: 'CI ID',
        name: 'ciId',
        customRender: (_data, row) => {
          const ciId = row.dataParse?.ci?.id;
          return <KanbanText lineClamp={2}>{ciId || ''}</KanbanText>;
        },
      },
      {
        title: 'Ci Type',
        name: 'ciType',
        customRender: (_data, row) => {
          const ciType = allCiTypes.data.find((x) => x.id === row.dataParse?.ci?.ciTypeId);
          if (!ciType) {
            return <></>;
          }
          return (
            <KanbanButton
              size='compact-xs'
              radius={'lg'}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                navigate(buildCiTypeUrl(ciType.id));
              }}>
              {ciType.name}
            </KanbanButton>
          );
        },
      },
      {
        title: 'Description',
        name: 'description',
        customRender: (_data, row) => {
          const description = row.dataParse?.ci?.description || '';
          return (
            <Tooltip label={description} multiline w={350} style={{ wordBreak: 'break-word' }}>
              <KanbanText lineClamp={2} style={{ wordBreak: 'break-word' }}>
                {description}
              </KanbanText>
            </Tooltip>
          );
        },
        width: '10%',
      },
    ];
  }, [selectedIds, listCi, listIdNotAllowSelect, allCiTypes.data, navigate]);

  const findListApprovalUser = useCallback((ciTypeId: number, pageInfo: PaginationRequestModel<EntityUserInfoResponse>) => {
    return CiManagementApi.getAllUserApprovalByCiType(ciTypeId, pageInfo);
  }, []);

  const updateListByCiType = useCallback(
    async (
      uniqueSelectedCiTypeId: Set<number>,
      rowData: CiManagementResponse,
      val: string,
      selectedIds: number[],
      updatedListCI: CiManagementResponse[],
    ) => {
      const pageInfo: PaginationRequestModel<EntityUserInfoResponse> = {
        page: 0,
        size: 10,
        search: val,
        sortBy: 'userName' as keyof EntityUserInfoResponse,
        isReverse: false,
      };

      let updatedListCIFinal = updatedListCI;
      for (const ciTypeId of uniqueSelectedCiTypeId) {
        const resApprovalUser = val ? await findListApprovalUser(ciTypeId, pageInfo) : undefined;
        const approvalUsers = resApprovalUser?.data.content || [];

        if (!val || approvalUsers.some((user) => user.userName === val)) {
          updatedListCIFinal = updatedListCIFinal.map((ci) => {
            if ((selectedIds.includes(ci.id) && ci.ciTypeId === ciTypeId) || rowData.id === ci.id) {
              return { ...ci, verifyUser: val };
            }
            return ci;
          });
        }
      }
      setListCi(updatedListCIFinal);
    },
    [findListApprovalUser],
  );

  const updateVerifyUser = useCallback(
    async (
      listCi: CiManagementResponse[],
      selectedIds: number[],
      rowData: CiManagementResponse,
      val: string,
      syncByCi: boolean,
      syncByCiType: boolean,
    ) => {
      let updatedListCI = [...listCi];

      if (syncByCi) {
        const uniqueSelectedCiTypeId = getUniqueSelectedCiTypeIds(listCi, selectedIds);
        await updateListByCiType(uniqueSelectedCiTypeId, rowData, val, selectedIds, updatedListCI);
      } else {
        updatedListCI = updateListDirectly(listCi, rowData, val, syncByCiType);
        setListCi(updatedListCI);
      }
    },
    [updateListByCiType],
  );

  const getUniqueSelectedCiTypeIds = (listCi: CiManagementResponse[], selectedIds: number[]) => {
    const listSelected = listCi.filter((x) => selectedIds.includes(x.id));
    const listSelectedCiTypeId = listSelected.map((x) => x.ciTypeId);
    return new Set(listSelectedCiTypeId);
  };

  const updateListDirectly = (listCi: CiManagementResponse[], rowData: CiManagementResponse, val: string, syncByCiType: boolean) => {
    return listCi.map((ci) => {
      if ((syncByCiType && rowData.ciTypeId === ci.ciTypeId) || rowData.id === ci.id) {
        return { ...ci, verifyUser: val };
      }
      return ci;
    });
  };

  const updatedColumns = useMemo(() => {
    const newColumns = [...columns];

    if (screenAction === ActionType.SEND) {
      newColumns.push({
        title: 'Approver',
        name: 'verifyUser',
        width: '10%',
        customRender: (_data, rowData) => {
          return (
            <UserPicker
              value={_data}
              disabled={listIdNotAllowSelect.includes(rowData.id)}
              disabledLoading
              loadUser={(pageInfo) => findListApprovalUser(rowData.ciTypeId, pageInfo)}
              onChange={(val) => {
                updateVerifyUser(listCi, selectedIds, rowData, val, syncByCi, syncByCiType);
              }}
            />
          );
        },
      });
    }

    const insertIndex = newColumns.length - 1;
    const newArrayColumns = listCiTypeAttribute.map((obj) => ({
      title: obj.name,
      name: `${obj.id}`,
      customRenderHeader: () => (
        <>
          <KanbanText truncate='end' fw={700} maw={'200px'}>
            {obj.name}
          </KanbanText>
        </>
      ),
      customRender: (_data: any, rowData: CiManagementResponse) => {
        const listAttribute = rowData.dataParse?.attributes;
        const objAttribute = (listAttribute || []).find((x: { ciTypeAttributeId: number }) => x.ciTypeAttributeId === obj.id);
        let valueView = objAttribute?.value || '';
        // update fix bug view value attribute REFERENCE
        if (CiTypeAttributeDataType.REFERENCE === obj.type && valueView) {
          valueView = obj.ciTypeReferenceData?.find((x) => `${x.ciId}` === valueView)?.ciAttributeValue || '';
        }
        return (
          <Tooltip label={valueView} multiline w={350} style={{ wordBreak: 'break-word' }}>
            <KanbanText lineClamp={2} style={{ wordBreak: 'break-word' }}>
              {valueView}
            </KanbanText>
          </Tooltip>
        );
      },
    }));

    newColumns.splice(insertIndex, 0, ...newArrayColumns);

    return newColumns;
  }, [
    columns,
    findListApprovalUser,
    listCi,
    listCiTypeAttribute,
    listIdNotAllowSelect,
    screenAction,
    selectedIds,
    syncByCi,
    syncByCiType,
    updateVerifyUser,
  ]);

  const tableProps: KanbanTableProps<CiManagementResponse> = useMemo(() => {
    return {
      title: 'List request send for approval',
      showNumericalOrderColumn: false,
      searchable: {
        enable: false,
        debounceTime: 300,
      },
      sortable: {
        enable: false,
      },
      columns: updatedColumns,
      data: listCi,
      pagination: {
        enable: true,
      },
      selectableRows: {
        enable: false,
      },
      onRowClicked: (data) => {
        if (!listIdNotAllowSelect.includes(data.id)) {
          setCurrentData(data);
          childRefViewDetail.current?.openPopupView();
        }
      },
      actions: {
        customAction: (data) => {
          return (
            <>
              <KanbanIconButton
                variant='transparent'
                size={'sm'}
                disabled={listIdNotAllowSelect.includes(data.id)}
                onClick={() => {
                  setCurrentData(data);
                  childRefViewDetail.current?.openPopupView();
                }}>
                <IconEye />
              </KanbanIconButton>
            </>
          );
        },
      },
    };
  }, [updatedColumns, listCi, listIdNotAllowSelect]);

  const onActionForApproval = (action: ActionType) => {
    const listCiSelected = listCi.filter((obj) => selectedIds.includes(obj.id));

    if (ActionType.SEND === action) {
      const checkDataVerifyUser = listCiSelected.some((obj) => !obj.verifyUser);
      if (checkDataVerifyUser) {
        NotificationError({
          message: 'Approval user must be not empty',
        });
        return;
      }
    }
    if (ActionType.REJECT === action && !reason) {
      setErrorMessage('The reason for rejection is required');
      NotificationError({
        message: 'The reason for rejection is required',
      });
      return;
    }
    if (openedModalConfirmApproval) {
      onCloseModalConfirmApproval();
    }

    const updatedList = listCiSelected.map((obj) => ({ ...obj, approvalComment: reason }));

    CiManagementApi.updateAction(action, updatedList)
      .then((_res) => {
        if (!isFromDetail && ActionType.SEND !== action && openModalConfirmViewChange) {
          openModalConfirmViewChange(actionType);
        }
        const selectedIdsClone: number[] = klona(selectedIds);
        const checkAllItemsSelected: boolean = selectedIdsClone.length + listIdNotAllowSelect.length === listCi.length;
        if (props.onCloseModal && checkAllItemsSelected) {
          // check if all item has selected -> closed modal
          props.onCloseModal(actionType);
        }
        setListIdNotAllowSelect((prev) => [...prev, ...selectedIdsClone]);
        setSelectedIds([]);
        if (onChangeData) {
          onChangeData(true);
        }
        NotificationSuccess({
          message: messageApprovalByAction(action || ActionType.SEND),
        });
      })
      .catch(() => {});
  };

  const onCloseModalConfirmApproval = () => {
    closeModalConfirmApproval();
    setErrorMessage('');
  };
  const handleConfirmAvoidAlertInvalidChange = () => {
    closeModalNotiInvalidCis();
    openModalConfirmApproval();
  };

  return (
    <>
      {/* modal view detail info of CI management draft */}
      <CiManagementDetailViewPopup ref={childRefViewDetail} initData={currentData} />
      <KanbanConfirmModal
        title={`Cis: ${listAlertCi} related to changes that in stages: ${LIST_ALERT_UPDATE_CI_IMPACT_CHANGE_STAGE.join(', ')}`}
        onConfirm={handleConfirmAvoidAlertInvalidChange}
        textConfirm='Confirm'
        onClose={closeModalNotiInvalidCis}
        opened={openedModalNotiInvalidCis}
        modalProps={{
          size: 'lg',
        }}>
        Do you still want to approve ci draft?
      </KanbanConfirmModal>
      <KanbanConfirmModal
        title={ActionType.REJECT === actionType ? `Reason for rejection` : `Reason for approval`}
        onConfirm={() => {
          onActionForApproval(actionType);
        }}
        textConfirm='Save'
        onClose={onCloseModalConfirmApproval}
        opened={openedModalConfirmApproval}
        modalProps={{
          size: 'lg',
        }}>
        <KanbanTextarea
          autosize
          maxLength={2000}
          minRows={4}
          maxRows={6}
          onChange={(e) => {
            setReason(e.target.value);
          }}
          placeholder={ActionType.REJECT === actionType ? `Enter the reason for rejection` : `Enter the reason for approval`}
          error={errorMessage}
        />
      </KanbanConfirmModal>
      <Group justify='space-between'>
        <Stack justify='flex-end' h={'50px'}>
          <KanbanText>
            Selected record ({selectedIds.length || 0}) - Total record ({listCi.length || 0})
          </KanbanText>
        </Stack>

        {ActionType.SEND === screenAction ? (
          <Flex align={'center'} gap={'xs'}>
            {listCi.length > 1 && (
              <Stack gap={'xs'}>
                <KanbanCheckbox
                  mb={0}
                  checked={syncByCiType}
                  disabled={syncByCi}
                  label={'Synchronize approvers by CI Type'}
                  onChange={() => {
                    setSyncByCiType(!syncByCiType);
                  }}
                />
                <KanbanCheckbox
                  mb={0}
                  checked={syncByCi}
                  disabled={syncByCiType}
                  label={'Synchronize approvers for currently selected CIs'}
                  onChange={() => {
                    setSyncByCi(!syncByCi);
                  }}
                />
              </Stack>
            )}
            <KanbanButton
              onClick={() => {
                setActionType(ActionType.SEND);
                onActionForApproval(ActionType.SEND);
              }}
              disabled={!selectedIds.length}>
              Send request for approval
            </KanbanButton>
          </Flex>
        ) : (
          <Flex justify={'flex-end'} gap={'xs'}>
            <KanbanButton
              color={'red'}
              onClick={() => {
                setActionType(ActionType.REJECT);
                openModalConfirmApproval();
              }}
              disabled={!selectedIds.length}>
              Reject
            </KanbanButton>
            <KanbanButton
              onClick={() => {
                setActionType(ActionType.APPROVE);
                handleApproveCis();
              }}
              disabled={!selectedIds.length}>
              Approve
            </KanbanButton>
          </Flex>
        )}
      </Group>
      <KanbanTable key={updatedColumns.length} {...tableProps} />
    </>
  );
};
CiRequestDetail.whyDidYouRender = true;
CiRequestDetail.displayName = 'CiRequestDetail';
export default CiRequestDetail;
