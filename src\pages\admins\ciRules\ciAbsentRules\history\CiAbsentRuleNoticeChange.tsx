import { KanbanA<PERSON>rdion, KanbanAccordionData, KanbanButton, KanbanText, KanbanTooltip } from 'kanban-design-system';
import { Flex, Group } from '@mantine/core';
import React, { useMemo } from 'react';
import { EntityAction } from '@models/AuditLog';
import { DataChangeLstWithActionObj } from '@models/CiReconciliationRule';
import styled from './CiAbsentRuleNoticeChange.module.scss';
import { RenderHighlightValue } from '@components/commonCi/RenderHighlightValue';
import { DataCiAbsentRuleCompare } from '@models/CiAbsentRule';

const renderActionColor = (action: EntityAction | EntityAction.UNKNOWN | undefined): string => {
  const defaultValue = 'gray';
  if (!action) {
    return defaultValue;
  }
  switch (action) {
    case EntityAction.CREATE:
      return 'green';
    case EntityAction.UPDATE:
      return 'orange';
    case EntityAction.DELETE:
      return 'red';
    default:
      return defaultValue;
  }
};
const RuleInfoTitle: React.FC<DataChangeLstWithActionObj> = (entry) => {
  return (
    <Flex gap='xs' align={'flex-start'}>
      <KanbanTooltip label={`${entry.titleAction}`}>
        <KanbanButton
          size='compact-xs'
          variant={'subtle'}
          c={'white'}
          fw={'700'}
          bg={renderActionColor(entry.action)}
          onClick={() => {
            // navigate(buildImpactedRuleDetailUrl(0, RuleAction.ADD));
          }}>
          {/* {entry.titleAction} */}
          <RenderHighlightValue text={entry.titleAction} />
        </KanbanButton>
      </KanbanTooltip>
    </Flex>
  );
};

const RuleChangeAccordion: React.FC<DataChangeLstWithActionObj> = (ruleDataChange) => {
  const dataAcc = useMemo((): KanbanAccordionData => {
    const res: KanbanAccordionData = {
      title: <RuleInfoTitle {...ruleDataChange} />,
      className: styled.accordion_content,
      content: (
        <Flex direction={'column'}>
          {ruleDataChange.dataChange &&
            ruleDataChange.dataChange.length > 0 &&
            ruleDataChange.dataChange.map((item) => (
              <Group key={item.key}>
                {/* -{' '} */}
                <KanbanText fw={700}>
                  <RenderHighlightValue text={item.key} />
                </KanbanText>
                :{' '}
                {!(EntityAction.CREATE === ruleDataChange.action) && (
                  <KanbanText>
                    <RenderHighlightValue text={item.oldValue} />
                  </KanbanText>
                )}
                {/* {item.newValue && (
                  <> */}
                {EntityAction.UPDATE === ruleDataChange.action && ` -> `}
                <KanbanText>
                  <RenderHighlightValue text={String(item.newValue)} />
                </KanbanText>
                {/* </>
                )} */}
                <br />
              </Group>
            ))}
        </Flex>
      ),
    };

    return { ...res, key: 'ruleChange' };
  }, [ruleDataChange]);
  return (
    ruleDataChange.dataChange &&
    ruleDataChange.dataChange.length > 0 && (
      <>
        <KanbanText fw='500'>Rule information</KanbanText>
        <KanbanAccordion
          mb={'xs'}
          className={styled.accordion}
          chevronPosition='left'
          chevronSize={'xs'}
          //control when init, which is opened
          defaultValue={['ruleChange']}
          data={[dataAcc]}
        />
      </>
    )
  );
};

type CiAbsentRuleNoticeChangeProps = {
  dataChange: string;
};

export const CiAbsentRuleNoticeChange = ({ dataChange }: CiAbsentRuleNoticeChangeProps) => {
  const parsedData: DataCiAbsentRuleCompare = useMemo(() => {
    try {
      return JSON.parse(dataChange);
    } catch (e) {
      console.error('CiAbsentRuleNoticeChange error', e);
      return {};
    }
  }, [dataChange]);
  const ciAbsentRule = parsedData.ciAbsentRule;
  const ruleDataChange = useMemo(() => {
    if (!ciAbsentRule) {
      return {};
    }
    const result: DataChangeLstWithActionObj = {
      action: ciAbsentRule.action,
      oldInfo: ciAbsentRule.oldInfo,
      newInfo: ciAbsentRule.newInfo,
      titleAction: ciAbsentRule.titleAction,
    };
    result.dataChange = [];

    const data = { ...ciAbsentRule };

    //rule change chi co 1 accordion nen chi can check co du lieu hight light 1 lan de expand
    for (const key in data.mapSelfChange) {
      const valueStr = String(data.mapSelfChange[key].oldValue);
      const newValue = String(data.mapSelfChange[key].newValue);

      result.dataChange.push({
        key: key,
        oldValue: valueStr,
        newValue: newValue,
      });
    }

    return result;
  }, [ciAbsentRule]);

  return (
    <>
      <RuleChangeAccordion {...ruleDataChange} />
    </>
  );
};

export default CiAbsentRuleNoticeChange;
