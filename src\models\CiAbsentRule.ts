import { ComboboxItem } from '@mantine/core';
import { EntityModelBase } from './EntityModelBase';
import { EntityAction } from './AuditLog';
import { Attribute } from './CiDetailCompare';

export enum CiAbsentRuleAction {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  VIEW = 'VIEW',
}

export enum RuleStatus {
  ENABLE = 'ENABLE',
  DISABLE = 'DISABLE',
}

export enum RULE_TAB {
  INFO = 'INFO',
  HISTORY = 'HISTORY',
}

export type CiAbsentRule = EntityModelBase & {
  name: string;
  status: RuleStatus;
  ciTypeId: number;
  ciTypeName?: string;
  description?: string;
  absenceThreshold: number;
  disposedThreshold: number;
  absenceThresholdUnit: IntervalUnitEnum;
  disposedThresholdUnit: IntervalUnitEnum;
};

export enum IntervalUnitEnum {
  MINUTE = 'MINUTE',
  HOUR = 'HOUR',
  DAY = 'DAY',
}

export const getComboboxIntervalUnitEnum = (): ComboboxItem[] => {
  return Object.values(IntervalUnitEnum).map((value) => ({
    value,
    label: value.toLowerCase(),
  }));
};

export const getIntervalUnitText = (value: IntervalUnitEnum | undefined): string => {
  if (!value) {
    return '';
  }

  const map: Record<IntervalUnitEnum, string> = {
    [IntervalUnitEnum.DAY]: `day(s)`,
    [IntervalUnitEnum.HOUR]: `hour(s)`,
    [IntervalUnitEnum.MINUTE]: `minute(s)`,
  };

  return map[value] || '';
};

export type CustomAttributeCompareModel = {
  oldInfo: Record<string, string>;
  newInfo: Record<string, string>;
  mapChildren: Record<string, CustomAttributeCompareModel>;
  mapSelfChange: Attribute;
  action: EntityAction;
  titleAction: string;
};

export type DataCiAbsentRuleCompare = {
  ciAbsentRule?: CustomAttributeCompareModel;
};

export type DataChangeWithAction = {
  key: string;
  oldValue?: string;
  newValue?: string | number;
  action?: EntityAction;
};
export type DataChangeLstWithActionObj = {
  dataChange?: DataChangeWithAction[];
  action?: EntityAction;
  titleAction?: string;
  oldInfo?: { [key: string]: string };
  newInfo?: { [key: string]: string };
};
