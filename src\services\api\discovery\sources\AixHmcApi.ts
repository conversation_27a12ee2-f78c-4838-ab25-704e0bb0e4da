import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import { JSONNode } from '@pages/admins/discovery/jsonTransform/helper/JsonTransformHelper';

export class AixHmcApi extends BaseApi {
  static baseUrl = BaseUrl.aixHmcs;

  static findAllDataAixHmc() {
    return BaseApi.getData<JSONNode>(`${this.baseUrl}`);
  }

  static findAllManagedSystem() {
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/managed-system`);
  }

  static findAllLogicalPartition(sourceId: number) {
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/logical-partition`, {
      sourceId: sourceId,
      resourcePath: 'rest/api/uom/LogicalPartition',
    });
  }
}
