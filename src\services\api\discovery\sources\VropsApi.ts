import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import { JSONNode } from '@pages/admins/discovery/jsonTransform/helper/JsonTransformHelper';

export class VropsApi extends BaseApi {
  static baseUrl = BaseUrl.vrops;

  static findAllClusters(sourceId: number) {
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/clusters?sourceId=${sourceId}`);
  }

  static findAllResources(sourceId: number) {
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/resources?sourceId=${sourceId}`);
  }
  static findAllHost(sourceId: number) {
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/host?sourceId=${sourceId}`);
  }
}
