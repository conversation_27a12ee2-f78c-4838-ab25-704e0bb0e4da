import { DeviceTypeEnum } from '@common/constants/DeviceTypeEnum';
import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import { JSONNode } from '@pages/admins/discovery/jsonTransform/helper/JsonTransformHelper';

export class SnmpApi extends BaseApi {
  static baseUrl = BaseUrl.snmps;

  static findAllDevice(deviceType: DeviceTypeEnum, sourceId: number) {
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/devices`, { deviceType: deviceType, sourceId: sourceId });
  }
}
