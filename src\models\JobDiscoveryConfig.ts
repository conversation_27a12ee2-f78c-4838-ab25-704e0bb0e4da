import { TimeUnitEnum } from '@common/constants/TimeUnitEnum';
import { JobGroupEnum } from '@common/constants/JobGroupEnum';
import { JobStatusEnum } from '@common/constants/JobStatusEnum';
import { JobTypeEnum } from '@common/constants/JobTypeEnum';

export type JobDiscoveryModel = {
  id?: number;
  jobGroup: JobGroupEnum;
  jobType?: JobTypeEnum;
  jobName: string;
  frequency: TimeUnitEnum;
  intervalTime: number;
  startDate: Date;
  endDate?: Date;
  retentionDay?: number;
  status?: JobStatusEnum;
  nextRun?: Date;
  dataSourceName?: string;
  configId: number;
  modifiedDate?: Date;
  systemJob: boolean;
};
