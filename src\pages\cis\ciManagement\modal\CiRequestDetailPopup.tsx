import { KanbanConfirmModal, KanbanText } from 'kanban-design-system';
import { useDisclosure } from '@mantine/hooks';
import React, { useState } from 'react';
import { forwardRef, useImperativeHandle } from 'react';
import type { CiManagementResponse } from '@api/CiManagementApi';
import { ActionType } from '@common/constants/CiManagement';
import CiRequestDetail from './CiRequestDetail';
import { Flex } from '@mantine/core';

type CiRequestDetailPopupProps = {
  listData: CiManagementResponse[];
  screenAction: ActionType;
  isFromDetail?: boolean;
  onCloseModal?: (val: ActionType) => void;
  openModalConfirmViewChange?: (val: ActionType) => void;
};

export type CiRequestDetailPopupMethods = {
  openPopupReview: () => void;
  closePopupReview: () => void;
};

export const CiRequestDetailPopup = forwardRef<CiRequestDetailPopupMethods, CiRequestDetailPopupProps>((props, ref) => {
  const { isFromDetail, listData, onCloseModal, openModalConfirmViewChange, screenAction } = props;
  const [openedModalReview, { close: closeModalReview, open: openModalReview }] = useDisclosure(false);
  const [useActionOnCloseModal, setUseActionOnCloseModal] = useState(false);
  const [actionType, setActionType] = useState<ActionType>(ActionType.SEND);

  useImperativeHandle<any, CiRequestDetailPopupMethods>(
    ref,
    () => ({
      openPopupReview: openModalReview,
      closePopupReview: closeModalReview,
    }),
    [openModalReview, closeModalReview],
  );

  const executeLogicWhenCloseModalReview = (action: ActionType) => {
    closeModalReview();
    if (onCloseModal) {
      onCloseModal(action);
    }
  };

  const executeWhenCloseModalReview = () => {
    closeModalReview();
    if (useActionOnCloseModal && onCloseModal) {
      onCloseModal(actionType);
      setUseActionOnCloseModal(false);
    }
  };

  const titleModalReview = screenAction === ActionType.SEND ? 'Send request for approval' : 'CI(s) approval information';

  return (
    <>
      <KanbanConfirmModal
        title={titleModalReview}
        textConfirm=''
        onClose={executeWhenCloseModalReview}
        opened={openedModalReview}
        modalProps={{
          size: '100%',
          closeOnClickOutside: false,
          actions: (
            <>
              <Flex justify='space-between' align='center' style={{ width: '95%' }}>
                <KanbanText style={{ fontSize: '12px', color: '#888', fontStyle: 'italic' }}>
                  Need permissions CI_VIEW_ADVANCED and CI_APPROVE to be on the approval list.
                </KanbanText>
              </Flex>
            </>
          ),
        }}>
        <CiRequestDetail
          listData={listData}
          screenAction={screenAction}
          onCloseModal={executeLogicWhenCloseModalReview}
          isFromDetail={isFromDetail}
          onChangeData={setUseActionOnCloseModal}
          openModalConfirmViewChange={openModalConfirmViewChange}
          actionType={actionType}
          setActionType={setActionType}
        />
      </KanbanConfirmModal>
    </>
  );
});
CiRequestDetailPopup.displayName = 'CiRequestDetailPopup';
export default CiRequestDetailPopup;
