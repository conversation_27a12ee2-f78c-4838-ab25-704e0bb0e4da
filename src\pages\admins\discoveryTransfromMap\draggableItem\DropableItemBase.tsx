import { Draggable, Droppable } from '@hello-pangea/dnd';
import { Paper, PaperProps, Tooltip } from '@mantine/core';
import React, { ReactNode, useEffect } from 'react';
import { DragTranfromMapType } from '@models/DiscoveryTransformMap';
import { useDraggableInPortal } from '@common/hooks/useDraggableInPortal';

export interface DropableItemBaseProps {
  item: DragTranfromMapType;
  index: number;
  classes: Record<string, string>;
  onClick?: () => void;

  // Customization props
  draggableIdPrefix: string;
  droppableIdPrefix?: string; // optional custom droppable id
  paperProps?: PaperProps;
  paperStyle?: React.CSSProperties;

  // Render props for content customization
  renderContent: (item: DragTranfromMapType) => ReactNode;
  dragableRef?: React.Ref<HTMLDivElement | null>;
}
export const DropableItemBase: React.FC<DropableItemBaseProps> = ({
  classes,
  dragableRef,
  index,
  item,
  onClick,
  paperProps,
  paperStyle,
  renderContent,
}) => {
  useEffect(() => {}, [item.hashId, item.id]);
  const renderDraggable = useDraggableInPortal();
  const DraggableChild = ({ item }: { item: DragTranfromMapType }) => (
    <Tooltip label={item.label} multiline withArrow openDelay={700}>
      <Paper
        className={item.selected ? classes.active : undefined}
        p='xs'
        radius='sm'
        shadow='md'
        withBorder
        onClick={onClick}
        style={{ ...paperStyle }}
        {...paperProps}>
        {renderContent(item)}
      </Paper>
    </Tooltip>
  );
  const stableDroppableId = `DROPABLE_CHILD_${item.id}_${item.hashId}`;
  return (
    <Draggable key={`${item.id}_${item.hashId || ''}`} index={index} draggableId={`DRAGGABLE_PARENT_${item.id}_${item.hashId}`}>
      {renderDraggable((dragProvided, snapshot) => (
        <div
          className={item.selected ? classes.active : undefined}
          ref={(node) => {
            dragProvided.innerRef(node);

            if (dragableRef) {
              if (typeof dragableRef === 'function') {
                dragableRef(node);
              } else {
                (dragableRef as React.MutableRefObject<HTMLDivElement | null>).current = node;
              }
            }
          }}
          {...dragProvided.draggableProps}
          {...dragProvided.dragHandleProps}
          style={{
            border: '1px solid #ccc',
            backgroundColor: snapshot.combineTargetFor ? '#dbe26fff' : (item.listChildren?.length ?? 0) > 0 ? '#d4dbe1' : '',
            marginBottom: 10,
            padding: (item.listChildren?.length ?? 0) > 0 || snapshot.combineTargetFor ? 4 : 0,
            borderRadius: 6,
            ...dragProvided.draggableProps.style,
          }}>
          <DraggableChild item={item} />

          {item.listChildren && (
            <Droppable key={`${item.id}_${item.hashId || ''}`} droppableId={stableDroppableId} type={`TYPE_${item.id}_${item.hashId}`}>
              {(dropProvided, _snapshot) => {
                // Calculate fixed height based on number of children to prevent layout shift
                const childrenCount = item.listChildren?.length || 0;
                const estimatedChildHeight = 50; // Approximate height of each child item (Paper + margin + spacing)
                // Always maintain height for original children count (without placeholder)
                const fixedHeight = childrenCount > 0 ? childrenCount * estimatedChildHeight : 'auto';

                return (
                  <div
                    ref={dropProvided.innerRef}
                    {...dropProvided.droppableProps}
                    style={{
                      // Fix: Always maintain consistent height during any drag operation
                      minHeight: fixedHeight,
                      transition: 'none', // Disable transition during drag
                    }}>
                    {item.listChildren?.map((child, childIndex) => (
                      <Draggable key={`${child.id}_${child.hashId}`} index={childIndex} draggableId={`DRAGGABLE_CHILD_${child.id}_${child.hashId}`}>
                        {renderDraggable((childProvided) => (
                          <div
                            ref={childProvided.innerRef}
                            {...childProvided.draggableProps}
                            {...childProvided.dragHandleProps}
                            style={{
                              marginTop: 10,
                              ...childProvided.draggableProps.style,
                            }}>
                            <DraggableChild item={child} />
                          </div>
                        ))}
                      </Draggable>
                    ))}
                    {/* Hide placeholder to prevent extra height */}
                    <div style={{ height: 0, overflow: 'hidden' }}>{dropProvided.placeholder}</div>
                  </div>
                );
              }}
            </Droppable>
          )}
        </div>
      ))}
    </Draggable>
  );
};
